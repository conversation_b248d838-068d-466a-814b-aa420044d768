"""
Inventory logic utilities for the Inventory Prediction System.
This module calculates smart inventory recommendations based on forecasted demand.
"""

import pandas as pd
import numpy as np
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, <PERSON><PERSON><PERSON>


def calculate_inventory_recommendations(forecast_df, buffer_percentage=20, 
                                      current_stock=0, lead_time_days=7,
                                      min_order_quantity=1, max_stock_level=None):
    """
    Calculate inventory recommendations based on forecast data.
    
    Args:
        forecast_df (pandas.DataFrame): Forecast data with 'ds', 'yhat' columns
        buffer_percentage (float): Safety stock buffer percentage (0-100)
        current_stock (float): Current inventory level
        lead_time_days (int): Lead time for restocking in days
        min_order_quantity (float): Minimum order quantity
        max_stock_level (float): Maximum stock level constraint
        
    Returns:
        pandas.DataFrame: Inventory recommendations
    """
    inventory_df = forecast_df.copy()
    
    # Calculate buffer stock
    buffer_multiplier = 1 + (buffer_percentage / 100)
    inventory_df['predicted_demand'] = inventory_df['yhat']
    inventory_df['buffer_stock'] = inventory_df['predicted_demand'] * (buffer_percentage / 100)
    inventory_df['recommended_stock'] = inventory_df['predicted_demand'] * buffer_multiplier
    
    # Calculate cumulative demand for lead time planning
    inventory_df['cumulative_demand'] = inventory_df['predicted_demand'].cumsum()
    
    # Calculate when to reorder (considering lead time)
    inventory_df['lead_time_demand'] = inventory_df['predicted_demand'].rolling(
        window=lead_time_days, min_periods=1).sum()
    
    # Calculate reorder point
    inventory_df['reorder_point'] = inventory_df['lead_time_demand'] * buffer_multiplier
    
    # Calculate order quantity needed
    inventory_df['current_stock_level'] = current_stock
    for i in range(1, len(inventory_df)):
        # Update stock level based on previous day's consumption
        prev_consumption = inventory_df.loc[i-1, 'predicted_demand']
        inventory_df.loc[i, 'current_stock_level'] = max(0, 
            inventory_df.loc[i-1, 'current_stock_level'] - prev_consumption)
    
    # Calculate order recommendations
    inventory_df['order_quantity'] = 0
    inventory_df['stock_status'] = 'Normal'
    
    for i in range(len(inventory_df)):
        current_level = inventory_df.loc[i, 'current_stock_level']
        reorder_point = inventory_df.loc[i, 'reorder_point']
        recommended_level = inventory_df.loc[i, 'recommended_stock']
        
        # Determine if reorder is needed
        if current_level <= reorder_point:
            order_qty = recommended_level - current_level
            
            # Apply minimum order quantity
            if order_qty > 0 and order_qty < min_order_quantity:
                order_qty = min_order_quantity
            
            # Apply maximum stock level constraint
            if max_stock_level and (current_level + order_qty) > max_stock_level:
                order_qty = max(0, max_stock_level - current_level)
            
            inventory_df.loc[i, 'order_quantity'] = order_qty
            inventory_df.loc[i, 'current_stock_level'] += order_qty
            
            # Update stock status
            if current_level <= reorder_point * 0.5:
                inventory_df.loc[i, 'stock_status'] = 'Critical'
            elif current_level <= reorder_point:
                inventory_df.loc[i, 'stock_status'] = 'Low'
        
        # Check for overstocking
        if max_stock_level and current_level > max_stock_level * 0.9:
            inventory_df.loc[i, 'stock_status'] = 'Overstock'
    
    # Calculate costs (optional - can be enhanced with actual cost data)
    inventory_df['holding_cost_per_unit'] = 0.1  # Default 10% of unit value per year
    inventory_df['estimated_holding_cost'] = (
        inventory_df['current_stock_level'] * inventory_df['holding_cost_per_unit'] / 365
    )
    
    # Round numerical columns for better display
    numerical_cols = ['predicted_demand', 'buffer_stock', 'recommended_stock', 
                     'reorder_point', 'order_quantity', 'current_stock_level']
    for col in numerical_cols:
        inventory_df[col] = inventory_df[col].round(2)
    
    return inventory_df


def create_inventory_dashboard_plot(inventory_df):
    """
    Create an interactive inventory dashboard plot.
    
    Args:
        inventory_df (pandas.DataFrame): Inventory recommendations data
        
    Returns:
        plotly.graph_objects.Figure: Interactive dashboard plot
    """
    fig = go.Figure()
    
    # Predicted demand
    fig.add_trace(go.Scatter(
        x=inventory_df['ds'],
        y=inventory_df['predicted_demand'],
        mode='lines+markers',
        name='Predicted Demand',
        line=dict(color='blue', width=2),
        marker=dict(size=4)
    ))
    
    # Recommended stock level
    fig.add_trace(go.Scatter(
        x=inventory_df['ds'],
        y=inventory_df['recommended_stock'],
        mode='lines',
        name='Recommended Stock',
        line=dict(color='green', width=2, dash='dash')
    ))
    
    # Current stock level
    fig.add_trace(go.Scatter(
        x=inventory_df['ds'],
        y=inventory_df['current_stock_level'],
        mode='lines+markers',
        name='Current Stock Level',
        line=dict(color='orange', width=2),
        marker=dict(size=4)
    ))
    
    # Reorder point
    fig.add_trace(go.Scatter(
        x=inventory_df['ds'],
        y=inventory_df['reorder_point'],
        mode='lines',
        name='Reorder Point',
        line=dict(color='red', width=1, dash='dot')
    ))
    
    # Highlight order points
    order_points = inventory_df[inventory_df['order_quantity'] > 0]
    if not order_points.empty:
        fig.add_trace(go.Scatter(
            x=order_points['ds'],
            y=order_points['current_stock_level'],
            mode='markers',
            name='Reorder Points',
            marker=dict(color='red', size=10, symbol='triangle-up')
        ))
    
    fig.update_layout(
        title='Inventory Management Dashboard',
        xaxis_title='Date',
        yaxis_title='Quantity',
        hovermode='x unified',
        template='plotly_white',
        height=500,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1
        )
    )
    
    return fig


def create_stock_status_chart(inventory_df):
    """
    Create a stock status distribution chart.
    
    Args:
        inventory_df (pandas.DataFrame): Inventory data
        
    Returns:
        plotly.graph_objects.Figure: Stock status chart
    """
    status_counts = inventory_df['stock_status'].value_counts()
    
    colors = {
        'Normal': 'green',
        'Low': 'orange', 
        'Critical': 'red',
        'Overstock': 'purple'
    }
    
    fig = go.Figure(data=[
        go.Bar(
            x=status_counts.index,
            y=status_counts.values,
            marker_color=[colors.get(status, 'gray') for status in status_counts.index]
        )
    ])
    
    fig.update_layout(
        title='Stock Status Distribution',
        xaxis_title='Stock Status',
        yaxis_title='Number of Days',
        template='plotly_white',
        height=400
    )
    
    return fig


def calculate_abc_analysis(historical_data, value_col='sales', quantity_col=None):
    """
    Perform ABC analysis on products based on sales value or quantity.
    
    Args:
        historical_data (pandas.DataFrame): Historical sales data
        value_col (str): Column name for sales value
        quantity_col (str): Column name for quantity (optional)
        
    Returns:
        pandas.DataFrame: ABC analysis results
    """
    if 'product' not in historical_data.columns:
        st.warning("Product column not found for ABC analysis")
        return None
    
    # Aggregate by product
    product_summary = historical_data.groupby('product').agg({
        value_col: 'sum'
    }).reset_index()
    
    if quantity_col and quantity_col in historical_data.columns:
        quantity_summary = historical_data.groupby('product')[quantity_col].sum()
        product_summary['total_quantity'] = quantity_summary.values
    
    # Sort by value
    product_summary = product_summary.sort_values(value_col, ascending=False)
    
    # Calculate cumulative percentage
    product_summary['cumulative_value'] = product_summary[value_col].cumsum()
    product_summary['cumulative_percentage'] = (
        product_summary['cumulative_value'] / product_summary[value_col].sum() * 100
    )
    
    # Assign ABC categories
    product_summary['abc_category'] = 'C'
    product_summary.loc[product_summary['cumulative_percentage'] <= 80, 'abc_category'] = 'A'
    product_summary.loc[
        (product_summary['cumulative_percentage'] > 80) & 
        (product_summary['cumulative_percentage'] <= 95), 'abc_category'
    ] = 'B'
    
    return product_summary


def generate_inventory_alerts(inventory_df, alert_threshold_days=7):
    """
    Generate inventory alerts based on stock levels and predictions.
    
    Args:
        inventory_df (pandas.DataFrame): Inventory data
        alert_threshold_days (int): Days ahead to check for alerts
        
    Returns:
        list: List of alert messages
    """
    alerts = []
    
    # Check for critical stock levels
    critical_days = inventory_df[inventory_df['stock_status'] == 'Critical']
    if not critical_days.empty:
        alerts.append({
            'type': 'critical',
            'message': f"🚨 Critical stock levels detected on {len(critical_days)} days",
            'details': critical_days[['ds', 'current_stock_level', 'predicted_demand']].to_dict('records')
        })
    
    # Check for upcoming reorders
    upcoming_orders = inventory_df[
        (inventory_df['order_quantity'] > 0) & 
        (inventory_df['ds'] <= datetime.now() + timedelta(days=alert_threshold_days))
    ]
    if not upcoming_orders.empty:
        total_order_qty = upcoming_orders['order_quantity'].sum()
        alerts.append({
            'type': 'reorder',
            'message': f"📦 {len(upcoming_orders)} reorders needed in next {alert_threshold_days} days (Total: {total_order_qty:.0f} units)",
            'details': upcoming_orders[['ds', 'order_quantity', 'stock_status']].to_dict('records')
        })
    
    # Check for overstock situations
    overstock_days = inventory_df[inventory_df['stock_status'] == 'Overstock']
    if not overstock_days.empty:
        alerts.append({
            'type': 'overstock',
            'message': f"📈 Overstock detected on {len(overstock_days)} days",
            'details': overstock_days[['ds', 'current_stock_level', 'recommended_stock']].to_dict('records')
        })
    
    # Check for high demand periods
    high_demand_threshold = inventory_df['predicted_demand'].quantile(0.9)
    high_demand_days = inventory_df[inventory_df['predicted_demand'] > high_demand_threshold]
    if not high_demand_days.empty:
        alerts.append({
            'type': 'high_demand',
            'message': f"📊 High demand periods detected on {len(high_demand_days)} days",
            'details': high_demand_days[['ds', 'predicted_demand', 'recommended_stock']].to_dict('records')
        })
    
    return alerts


def optimize_inventory_parameters(historical_data, forecast_data, cost_per_unit=1.0, 
                                holding_cost_rate=0.1, stockout_cost_rate=5.0):
    """
    Optimize inventory parameters to minimize total costs.
    
    Args:
        historical_data (pandas.DataFrame): Historical sales data
        forecast_data (pandas.DataFrame): Forecast data
        cost_per_unit (float): Cost per unit
        holding_cost_rate (float): Holding cost as percentage of unit cost per year
        stockout_cost_rate (float): Stockout cost multiplier
        
    Returns:
        dict: Optimized parameters
    """
    # Calculate demand statistics
    demand_mean = forecast_data['yhat'].mean()
    demand_std = forecast_data['yhat'].std()
    
    # Economic Order Quantity (EOQ) calculation
    annual_demand = demand_mean * 365
    ordering_cost = cost_per_unit * 0.1  # Assume 10% of unit cost
    holding_cost_per_unit = cost_per_unit * holding_cost_rate
    
    eoq = np.sqrt((2 * annual_demand * ordering_cost) / holding_cost_per_unit)
    
    # Safety stock calculation (using service level approach)
    service_level = 0.95  # 95% service level
    z_score = 1.645  # For 95% service level
    lead_time_days = 7
    
    safety_stock = z_score * demand_std * np.sqrt(lead_time_days)
    reorder_point = demand_mean * lead_time_days + safety_stock
    
    # Optimal buffer percentage
    optimal_buffer = (safety_stock / demand_mean) * 100
    
    return {
        'eoq': round(eoq, 2),
        'safety_stock': round(safety_stock, 2),
        'reorder_point': round(reorder_point, 2),
        'optimal_buffer_percentage': round(optimal_buffer, 1),
        'recommended_service_level': service_level * 100
    }
