# 📊 Universal Data Analysis System

A comprehensive web-based platform for analyzing any type of data with advanced visualization and forecasting capabilities.

## 🚀 Features

- **📁 Universal Data Upload**: Works with any CSV or Excel file
- **🔍 Smart Column Detection**: Automatically identifies data types and suggests analysis
- **📊 Interactive Visualizations**: Dynamic charts that adapt to your data structure
- **🔮 Universal Forecasting**: Time-series prediction for any numerical data
- **📈 Advanced Analytics**: Statistical analysis, correlation, outlier detection
- **📋 Professional Reports**: Export capabilities with CSV downloads
- **🎨 Modern UI**: Beautiful, responsive interface with intuitive navigation

## 📁 Application Versions

### 🚀 **`app.py`** - Main Application (Recommended)
- **Universal data analysis** for any type of data
- **Modern UI** with beautiful gradients and responsive design
- **Basic dependencies** - easy to install and run
- **All-in-one file** - no complex module structure
- **Works immediately** with just Streamlit, Pandas, and Plotly

### 🔬 **`app_advanced.py`** - Advanced Features
- **Full-featured** with Prophet/ARIMA forecasting models
- **Complete inventory management** tools
- **PDF/Excel export** capabilities
- **Modular structure** with separate utility files
- **Requires more dependencies** - Prophet, ARIMA, ReportLab, etc.

## 🎯 Quick Start

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Easy Installation (Recommended)

1. **Install basic dependencies**
   ```bash
   pip install streamlit pandas plotly numpy
   ```

2. **Run the main application**
   ```bash
   python -m streamlit run app.py
   ```

3. **Open your browser**
   - Navigate to `http://localhost:8501`
   - Start analyzing your data immediately!

### Advanced Installation (Optional)

For the full-featured version with advanced ML models:

1. **Install all dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the advanced application**
   ```bash
   python -m streamlit run app_advanced.py
   ```

## 📋 Data Requirements

Your data should include these columns:

### Required
- **Date**: Sales date (any common date format: YYYY-MM-DD, MM/DD/YYYY, etc.)
- **Sales/Demand**: Quantity sold or demanded (numerical values)

### Optional
- **Product**: Product identifier or name
- **Store**: Store or location identifier
- **Price**: Unit price information

### Supported Formats
- CSV files (.csv)
- Excel files (.xlsx, .xls)

### Example Data Format
```csv
Date,Product,Sales,Store,Price
2023-01-01,Product A,100,Store 1,10.99
2023-01-02,Product B,150,Store 1,15.50
2023-01-03,Product A,120,Store 2,10.99
```

## 🔧 Usage Guide

### 1. Data Upload & Cleaning
- Upload your CSV or Excel file
- Map columns to required fields
- Clean and validate your data
- Review data quality metrics

### 2. Exploratory Data Analysis
- View sales trends and patterns
- Analyze seasonal behavior
- Examine product and store performance
- Get statistical insights

### 3. Forecasting
- Choose forecast period (30-365 days)
- Select model (Prophet or ARIMA)
- Configure model parameters
- Generate and validate predictions

### 4. Inventory Planning
- Set safety stock buffer percentage
- Configure current stock levels
- Define lead times and constraints
- Generate smart reorder recommendations

### 5. Reports & Export
- Export results to Excel or PDF
- Include forecast and inventory data
- Generate professional reports
- Download for further analysis

## 🤖 AI Assistant

The built-in AI assistant can help with:
- Understanding forecast results
- Interpreting inventory recommendations
- Explaining model differences
- Troubleshooting issues
- Best practices guidance

## 🔮 Forecasting Models

### Prophet
- **Best for**: Data with strong seasonal patterns
- **Advantages**: Handles missing data, automatic seasonality detection
- **Use when**: You have clear weekly/monthly/yearly patterns

### ARIMA
- **Best for**: Stationary time series data
- **Advantages**: Classical statistical approach, well-established
- **Use when**: You have consistent, stable demand patterns

## 📦 Inventory Management

### Key Concepts

**Safety Stock (Buffer Stock)**
- Extra inventory to protect against demand variability
- Calculated as percentage of expected demand
- Higher buffer = lower stockout risk but higher costs

**Reorder Point**
- Inventory level that triggers a new order
- Formula: (Average daily demand × Lead time) + Safety stock

**Economic Order Quantity (EOQ)**
- Optimal order quantity that minimizes total costs
- Balances ordering costs and holding costs

## 🚀 Deployment

### Streamlit Cloud

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy on Streamlit Cloud**
   - Go to [share.streamlit.io](https://share.streamlit.io)
   - Connect your GitHub repository
   - Select the main branch and `app.py`
   - Click "Deploy"

### Render.com

1. **Create `render.yaml`** (optional)
   ```yaml
   services:
     - type: web
       name: inventory-prediction-system
       env: python
       buildCommand: pip install -r requirements.txt
       startCommand: streamlit run app.py --server.port $PORT --server.address 0.0.0.0
   ```

2. **Deploy on Render**
   - Connect your GitHub repository
   - Select "Web Service"
   - Configure build and start commands
   - Deploy

### Local Development

```bash
# Install in development mode
pip install -r requirements.txt

# Run with debug mode
streamlit run app.py --logger.level debug

# Run on custom port
streamlit run app.py --server.port 8502
```

## 📁 Project Structure

```
Shoona_project/
├── app.py                 # Main Streamlit application
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── utils/                # Utility modules
│   ├── data_cleaning.py  # Data preprocessing functions
│   ├── forecasting.py    # ML forecasting models
│   ├── inventory_logic.py # Inventory calculations
│   └── report_exporter.py # Export functionality
├── pages/                # Streamlit pages
│   ├── data_upload.py    # Data upload interface
│   ├── eda.py           # Exploratory data analysis
│   ├── forecasting.py   # Forecasting interface
│   ├── inventory.py     # Inventory planning
│   ├── reports.py       # Reports and export
│   └── ai_assistant.py  # AI assistant
└── assets/              # Static assets (if any)
```

## 🔧 Configuration

### Environment Variables
- `OPENAI_API_KEY`: For AI assistant integration (optional)

### Model Parameters
- **Prophet**: Seasonality mode, yearly/weekly seasonality
- **ARIMA**: Auto-detection or manual (p,d,q) parameters
- **Inventory**: Buffer percentage, lead times, cost parameters

## 🐛 Troubleshooting

### Common Issues

**"No module named 'prophet'"**
```bash
pip install prophet
# or
conda install -c conda-forge prophet
```

**"ARIMA model failed to fit"**
- Check if your data has enough historical points (minimum 30)
- Try different ARIMA parameters
- Consider using Prophet instead

**"Forecast generation failed"**
- Ensure date column is properly formatted
- Check for missing or negative values
- Verify minimum data requirements

**"Export failed"**
- Check if you have write permissions
- Ensure sufficient disk space
- Try exporting smaller date ranges

## 📊 Performance Tips

- **Data Size**: Works best with 30-1000 days of historical data
- **Frequency**: Daily aggregation recommended for most use cases
- **Models**: Try both Prophet and ARIMA, compare accuracy
- **Parameters**: Start with default settings, then optimize

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check this README and in-app help
- **AI Assistant**: Use the built-in assistant for guidance
- **Issues**: Report bugs via GitHub issues
- **Questions**: Use the discussion forum

## 🔮 Future Enhancements

- Real-time data integration
- Advanced ML models (LSTM, XGBoost)
- Multi-location inventory optimization
- Automated email reports
- Mobile-responsive design
- API endpoints for integration

---

**Built with ❤️ using Streamlit, Prophet, and modern data science tools.**
