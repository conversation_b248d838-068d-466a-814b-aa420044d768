# 📦 Inventory Prediction System

A comprehensive web-based system for demand forecasting and intelligent inventory management using machine learning models.

## 🚀 Features

- **📊 Data Processing**: Upload and clean historical sales data (CSV/Excel)
- **📈 Exploratory Analysis**: Interactive visualizations and insights
- **🔮 ML Forecasting**: Prophet and ARIMA models for demand prediction
- **📦 Smart Inventory**: Automated reorder recommendations with buffer stock
- **📋 Professional Reports**: Export to Excel and PDF formats
- **🤖 AI Assistant**: Get help and explanations for your analysis
- **💻 Code View**: See the underlying Python code for each operation

## 🎯 Quick Start

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Shoona_project
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   streamlit run app.py
   ```

4. **Open your browser**
   - The app will automatically open at `http://localhost:8501`
   - If not, navigate to the URL shown in your terminal

## 📋 Data Requirements

Your data should include these columns:

### Required
- **Date**: Sales date (any common date format: YYYY-MM-DD, MM/DD/YYYY, etc.)
- **Sales/Demand**: Quantity sold or demanded (numerical values)

### Optional
- **Product**: Product identifier or name
- **Store**: Store or location identifier
- **Price**: Unit price information

### Supported Formats
- CSV files (.csv)
- Excel files (.xlsx, .xls)

### Example Data Format
```csv
Date,Product,Sales,Store,Price
2023-01-01,Product A,100,Store 1,10.99
2023-01-02,Product B,150,Store 1,15.50
2023-01-03,Product A,120,Store 2,10.99
```

## 🔧 Usage Guide

### 1. Data Upload & Cleaning
- Upload your CSV or Excel file
- Map columns to required fields
- Clean and validate your data
- Review data quality metrics

### 2. Exploratory Data Analysis
- View sales trends and patterns
- Analyze seasonal behavior
- Examine product and store performance
- Get statistical insights

### 3. Forecasting
- Choose forecast period (30-365 days)
- Select model (Prophet or ARIMA)
- Configure model parameters
- Generate and validate predictions

### 4. Inventory Planning
- Set safety stock buffer percentage
- Configure current stock levels
- Define lead times and constraints
- Generate smart reorder recommendations

### 5. Reports & Export
- Export results to Excel or PDF
- Include forecast and inventory data
- Generate professional reports
- Download for further analysis

## 🤖 AI Assistant

The built-in AI assistant can help with:
- Understanding forecast results
- Interpreting inventory recommendations
- Explaining model differences
- Troubleshooting issues
- Best practices guidance

## 🔮 Forecasting Models

### Prophet
- **Best for**: Data with strong seasonal patterns
- **Advantages**: Handles missing data, automatic seasonality detection
- **Use when**: You have clear weekly/monthly/yearly patterns

### ARIMA
- **Best for**: Stationary time series data
- **Advantages**: Classical statistical approach, well-established
- **Use when**: You have consistent, stable demand patterns

## 📦 Inventory Management

### Key Concepts

**Safety Stock (Buffer Stock)**
- Extra inventory to protect against demand variability
- Calculated as percentage of expected demand
- Higher buffer = lower stockout risk but higher costs

**Reorder Point**
- Inventory level that triggers a new order
- Formula: (Average daily demand × Lead time) + Safety stock

**Economic Order Quantity (EOQ)**
- Optimal order quantity that minimizes total costs
- Balances ordering costs and holding costs

## 🚀 Deployment

### Streamlit Cloud

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy on Streamlit Cloud**
   - Go to [share.streamlit.io](https://share.streamlit.io)
   - Connect your GitHub repository
   - Select the main branch and `app.py`
   - Click "Deploy"

### Render.com

1. **Create `render.yaml`** (optional)
   ```yaml
   services:
     - type: web
       name: inventory-prediction-system
       env: python
       buildCommand: pip install -r requirements.txt
       startCommand: streamlit run app.py --server.port $PORT --server.address 0.0.0.0
   ```

2. **Deploy on Render**
   - Connect your GitHub repository
   - Select "Web Service"
   - Configure build and start commands
   - Deploy

### Local Development

```bash
# Install in development mode
pip install -r requirements.txt

# Run with debug mode
streamlit run app.py --logger.level debug

# Run on custom port
streamlit run app.py --server.port 8502
```

## 📁 Project Structure

```
Shoona_project/
├── app.py                 # Main Streamlit application
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── utils/                # Utility modules
│   ├── data_cleaning.py  # Data preprocessing functions
│   ├── forecasting.py    # ML forecasting models
│   ├── inventory_logic.py # Inventory calculations
│   └── report_exporter.py # Export functionality
├── pages/                # Streamlit pages
│   ├── data_upload.py    # Data upload interface
│   ├── eda.py           # Exploratory data analysis
│   ├── forecasting.py   # Forecasting interface
│   ├── inventory.py     # Inventory planning
│   ├── reports.py       # Reports and export
│   └── ai_assistant.py  # AI assistant
└── assets/              # Static assets (if any)
```

## 🔧 Configuration

### Environment Variables
- `OPENAI_API_KEY`: For AI assistant integration (optional)

### Model Parameters
- **Prophet**: Seasonality mode, yearly/weekly seasonality
- **ARIMA**: Auto-detection or manual (p,d,q) parameters
- **Inventory**: Buffer percentage, lead times, cost parameters

## 🐛 Troubleshooting

### Common Issues

**"No module named 'prophet'"**
```bash
pip install prophet
# or
conda install -c conda-forge prophet
```

**"ARIMA model failed to fit"**
- Check if your data has enough historical points (minimum 30)
- Try different ARIMA parameters
- Consider using Prophet instead

**"Forecast generation failed"**
- Ensure date column is properly formatted
- Check for missing or negative values
- Verify minimum data requirements

**"Export failed"**
- Check if you have write permissions
- Ensure sufficient disk space
- Try exporting smaller date ranges

## 📊 Performance Tips

- **Data Size**: Works best with 30-1000 days of historical data
- **Frequency**: Daily aggregation recommended for most use cases
- **Models**: Try both Prophet and ARIMA, compare accuracy
- **Parameters**: Start with default settings, then optimize

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check this README and in-app help
- **AI Assistant**: Use the built-in assistant for guidance
- **Issues**: Report bugs via GitHub issues
- **Questions**: Use the discussion forum

## 🔮 Future Enhancements

- Real-time data integration
- Advanced ML models (LSTM, XGBoost)
- Multi-location inventory optimization
- Automated email reports
- Mobile-responsive design
- API endpoints for integration

---

**Built with ❤️ using Streamlit, Prophet, and modern data science tools.**
