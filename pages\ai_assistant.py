"""
AI Assistant Page for the Inventory Prediction System.
"""

import streamlit as st
import pandas as pd
from datetime import datetime


def show_ai_assistant_page():
    """Display the AI assistant page."""
    st.title("🤖 AI Assistant")
    st.markdown("Get help and explanations about your inventory analysis.")
    
    # AI Assistant configuration
    st.subheader("⚙️ Assistant Configuration")
    
    col1, col2 = st.columns(2)
    
    with col1:
        use_openai = st.checkbox(
            "Enable OpenAI Integration",
            value=False,
            help="Connect to OpenAI API for advanced AI assistance"
        )
    
    with col2:
        if use_openai:
            openai_api_key = st.text_input(
                "OpenAI API Key",
                type="password",
                help="Enter your OpenAI API key"
            )
        else:
            st.info("Using built-in knowledge base")
    
    # Quick help section
    st.subheader("❓ Quick Help")
    
    help_categories = {
        "📊 Data & Forecasting": [
            "What does this forecast mean?",
            "How accurate is my forecast?",
            "What is the difference between Prophet and ARIMA?",
            "How do I interpret confidence intervals?",
            "What causes forecast uncertainty?"
        ],
        "📦 Inventory Management": [
            "What is safety stock and why do I need it?",
            "How is the reorder point calculated?",
            "What does buffer stock percentage mean?",
            "When should I place orders?",
            "How do I handle seasonal demand?"
        ],
        "📈 Analysis & Insights": [
            "How do I read the inventory dashboard?",
            "What do the stock status colors mean?",
            "How should I interpret the cost analysis?",
            "What are the key performance indicators?",
            "How do I optimize my inventory levels?"
        ],
        "🔧 Technical Support": [
            "How do I upload my data correctly?",
            "What data format is required?",
            "Why is my forecast not generating?",
            "How do I export my results?",
            "What do the error messages mean?"
        ]
    }
    
    # Display help categories
    for category, questions in help_categories.items():
        with st.expander(category):
            for question in questions:
                if st.button(question, key=f"help_{question}"):
                    answer = get_predefined_answer(question)
                    st.info(f"**Q: {question}**\n\n{answer}")
    
    # Chat interface
    st.subheader("💬 Ask a Question")
    
    # Initialize chat history
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    # Chat input
    user_question = st.text_input(
        "Ask me anything about inventory forecasting:",
        placeholder="e.g., How do I interpret my forecast results?",
        key="user_question"
    )
    
    if st.button("Ask", type="primary") and user_question:
        # Add user question to history
        st.session_state.chat_history.append({
            'type': 'user',
            'message': user_question,
            'timestamp': datetime.now()
        })
        
        # Generate response
        if use_openai and 'openai_api_key' in locals() and openai_api_key:
            response = get_openai_response(user_question, openai_api_key)
        else:
            response = get_built_in_response(user_question)
        
        # Add assistant response to history
        st.session_state.chat_history.append({
            'type': 'assistant',
            'message': response,
            'timestamp': datetime.now()
        })
    
    # Display chat history
    if st.session_state.chat_history:
        st.subheader("💬 Conversation History")
        
        for chat in reversed(st.session_state.chat_history[-10:]):  # Show last 10 messages
            if chat['type'] == 'user':
                st.markdown(f"**You ({chat['timestamp'].strftime('%H:%M')}):** {chat['message']}")
            else:
                st.markdown(f"**Assistant ({chat['timestamp'].strftime('%H:%M')}):** {chat['message']}")
            st.markdown("---")
        
        if st.button("Clear Chat History"):
            st.session_state.chat_history = []
            st.rerun()
    
    # Context-aware suggestions
    st.subheader("💡 Contextual Suggestions")
    
    suggestions = get_contextual_suggestions()
    
    if suggestions:
        st.markdown("Based on your current analysis, you might want to ask:")
        for suggestion in suggestions:
            if st.button(suggestion, key=f"suggestion_{suggestion}"):
                # Auto-fill the question
                st.session_state.user_question = suggestion
                st.rerun()
    
    # Knowledge base
    st.subheader("📚 Knowledge Base")
    
    with st.expander("Forecasting Models Explained"):
        st.markdown("""
        **Prophet**
        - Developed by Facebook for time series forecasting
        - Handles seasonality and holidays automatically
        - Good for data with strong seasonal patterns
        - More robust to missing data and outliers
        - Provides uncertainty intervals
        
        **ARIMA (AutoRegressive Integrated Moving Average)**
        - Classical statistical forecasting method
        - Good for stationary time series data
        - Requires parameter tuning (p, d, q)
        - Works well with consistent patterns
        - More sensitive to data quality
        """)
    
    with st.expander("Inventory Management Concepts"):
        st.markdown("""
        **Safety Stock (Buffer Stock)**
        - Extra inventory to protect against demand variability
        - Calculated as percentage of expected demand
        - Higher buffer = lower stockout risk but higher costs
        
        **Reorder Point**
        - Inventory level that triggers a new order
        - Calculated as: Lead time demand + Safety stock
        - Ensures stock availability during replenishment
        
        **Economic Order Quantity (EOQ)**
        - Optimal order quantity that minimizes total costs
        - Balances ordering costs and holding costs
        - Formula: √(2 × Annual demand × Ordering cost / Holding cost)
        
        **Lead Time**
        - Time between placing an order and receiving it
        - Critical for calculating reorder points
        - Should include processing, shipping, and receiving time
        """)
    
    with st.expander("Data Quality Guidelines"):
        st.markdown("""
        **Required Data**
        - Date column (any standard date format)
        - Sales/demand quantity (numerical)
        - At least 30 days of historical data
        
        **Optional Data**
        - Product identifiers
        - Store/location information
        - Price information
        - Promotional flags
        
        **Data Quality Tips**
        - Remove or fix obvious outliers
        - Ensure consistent date formatting
        - Fill missing values appropriately
        - Remove duplicate records
        - Validate negative values
        """)


def get_predefined_answer(question):
    """Get predefined answers for common questions."""
    answers = {
        "What does this forecast mean?": 
            "Your forecast shows predicted demand for future periods based on historical patterns. "
            "The main line shows expected demand, while the shaded area represents uncertainty bounds. "
            "Use this to plan inventory levels and identify peak demand periods.",
        
        "How accurate is my forecast?":
            "Forecast accuracy depends on data quality, historical patterns, and model choice. "
            "Check the MAPE (Mean Absolute Percentage Error) - lower is better. "
            "Values under 20% are generally good for inventory planning.",
        
        "What is the difference between Prophet and ARIMA?":
            "Prophet is better for data with strong seasonality and handles missing data well. "
            "ARIMA is more traditional and works well with consistent, stationary data. "
            "Try both and compare accuracy metrics to choose the best for your data.",
        
        "What is safety stock and why do I need it?":
            "Safety stock is extra inventory to protect against demand variability and supply delays. "
            "It prevents stockouts when demand is higher than expected or deliveries are delayed. "
            "Higher safety stock reduces stockout risk but increases holding costs.",
        
        "How is the reorder point calculated?":
            "Reorder point = (Average daily demand × Lead time) + Safety stock. "
            "This ensures you have enough inventory to cover demand during the replenishment period "
            "plus a buffer for unexpected demand spikes.",
        
        "What does buffer stock percentage mean?":
            "Buffer stock percentage is the extra inventory above expected demand. "
            "For example, 20% buffer means ordering 120 units when you expect to sell 100. "
            "Higher percentages provide more protection but cost more to hold."
    }
    
    return answers.get(question, 
        "I don't have a specific answer for that question, but I can help with general "
        "inventory forecasting concepts. Try asking about forecasting models, inventory "
        "management, or data requirements.")


def get_built_in_response(question):
    """Generate response using built-in knowledge."""
    question_lower = question.lower()
    
    # Keyword-based responses
    if any(word in question_lower for word in ['forecast', 'predict', 'future']):
        return ("Forecasting uses historical data to predict future demand. The system analyzes "
                "patterns, trends, and seasonality to generate predictions. Key factors affecting "
                "accuracy include data quality, historical patterns, and external factors.")
    
    elif any(word in question_lower for word in ['inventory', 'stock', 'reorder']):
        return ("Inventory management involves balancing stock levels to meet demand while "
                "minimizing costs. Key concepts include safety stock, reorder points, and "
                "economic order quantities. The system calculates optimal levels based on "
                "your forecast and business parameters.")
    
    elif any(word in question_lower for word in ['accuracy', 'error', 'reliable']):
        return ("Forecast accuracy is measured using metrics like MAE, RMSE, and MAPE. "
                "Lower values indicate better accuracy. Factors affecting accuracy include "
                "data quality, seasonality strength, and forecast horizon length.")
    
    elif any(word in question_lower for word in ['prophet', 'arima', 'model']):
        return ("Prophet and ARIMA are different forecasting approaches. Prophet handles "
                "seasonality automatically and is robust to missing data. ARIMA is more "
                "traditional and works well with stationary data. Choose based on your "
                "data characteristics and accuracy results.")
    
    elif any(word in question_lower for word in ['cost', 'expense', 'budget']):
        return ("Inventory costs include ordering costs, holding costs, and stockout costs. "
                "The system helps optimize these by calculating economic order quantities "
                "and appropriate safety stock levels. Balance service level with cost efficiency.")
    
    else:
        return ("I can help with questions about forecasting, inventory management, data "
                "requirements, model selection, and cost optimization. Please ask a more "
                "specific question about these topics.")


def get_openai_response(question, api_key):
    """Generate response using OpenAI API (placeholder)."""
    try:
        # This would integrate with OpenAI API
        # For now, return a placeholder response
        return ("OpenAI integration is not fully implemented in this demo. "
                "This would provide advanced AI-powered responses to your questions "
                "about inventory forecasting and management.")
    except Exception as e:
        return f"Error connecting to OpenAI: {str(e)}. Using built-in responses instead."


def get_contextual_suggestions():
    """Generate contextual suggestions based on current state."""
    suggestions = []
    
    if st.session_state.forecast_data is not None:
        suggestions.extend([
            "How do I interpret my forecast confidence intervals?",
            "What should I do about high forecast uncertainty?",
            "How do I validate my forecast accuracy?"
        ])
    
    if st.session_state.inventory_data is not None:
        suggestions.extend([
            "Why do I have critical stock alerts?",
            "How can I optimize my buffer stock percentage?",
            "What's the best reorder strategy for my business?"
        ])
    
    if not st.session_state.data_loaded:
        suggestions.extend([
            "What data format should I use for upload?",
            "How much historical data do I need?",
            "What if my data has missing values?"
        ])
    
    return suggestions[:3]  # Return top 3 suggestions
