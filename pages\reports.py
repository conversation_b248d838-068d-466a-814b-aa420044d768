"""
Reports and Export Page for the Inventory Prediction System.
"""

import streamlit as st
import pandas as pd
import numpy as np
import base64
import io
from datetime import datetime
from utils.report_exporter import (
    export_to_excel, create_pdf_report, create_download_link
)


def show_code_snippet(code, description):
    """Show code snippet if code view is enabled."""
    if st.session_state.show_code:
        with st.expander(f"💻 Code: {description}"):
            st.code(code, language='python')


def show_reports_page():
    """Display the reports and export page."""
    st.title("📋 Reports & Export")
    st.markdown("Generate and download comprehensive reports of your analysis.")
    
    # Check if data is available
    has_forecast = st.session_state.forecast_data is not None
    has_inventory = st.session_state.inventory_data is not None
    has_cleaned_data = st.session_state.cleaned_data is not None
    
    if not has_cleaned_data:
        st.warning("⚠️ No data available. Please upload and clean your data first.")
        return
    
    # Report configuration
    st.subheader("📊 Report Configuration")
    
    col1, col2 = st.columns(2)
    
    with col1:
        include_forecast = st.checkbox(
            "Include Forecast Data",
            value=has_forecast,
            disabled=not has_forecast,
            help="Include forecasting results in the report"
        )
        
        include_inventory = st.checkbox(
            "Include Inventory Plan",
            value=has_inventory,
            disabled=not has_inventory,
            help="Include inventory recommendations in the report"
        )
    
    with col2:
        report_title = st.text_input(
            "Report Title",
            value="Inventory Prediction Report",
            help="Custom title for your report"
        )
        
        company_name = st.text_input(
            "Company Name (Optional)",
            value="",
            help="Your company name for the report header"
        )
    
    # Report summary
    st.subheader("📋 Report Summary")
    
    summary_data = []
    
    if has_cleaned_data:
        df = st.session_state.cleaned_data
        column_mapping = st.session_state.column_mapping
        date_col = column_mapping.get('date')
        sales_col = column_mapping.get('sales')
        
        if date_col and sales_col:
            summary_data.extend([
                ("Data Period", f"{df[date_col].min().strftime('%Y-%m-%d')} to {df[date_col].max().strftime('%Y-%m-%d')}"),
                ("Total Records", f"{len(df):,}"),
                ("Total Sales", f"{df[sales_col].sum():,.0f}"),
                ("Average Daily Sales", f"{df.groupby(date_col)[sales_col].sum().mean():.1f}")
            ])
    
    if has_forecast:
        forecast_data = st.session_state.forecast_data
        summary_data.extend([
            ("Forecast Period", f"{len(forecast_data)} days"),
            ("Forecast Model", forecast_data['model'].iloc[0] if 'model' in forecast_data.columns else "Unknown"),
            ("Avg Forecasted Demand", f"{forecast_data['yhat'].mean():.1f}"),
            ("Total Forecasted Demand", f"{forecast_data['yhat'].sum():.0f}")
        ])
    
    if has_inventory:
        inventory_data = st.session_state.inventory_data
        total_orders = inventory_data['order_quantity'].sum()
        reorder_days = len(inventory_data[inventory_data['order_quantity'] > 0])
        
        summary_data.extend([
            ("Total Orders Recommended", f"{total_orders:.0f}"),
            ("Reorder Days", f"{reorder_days}"),
            ("Critical Stock Days", f"{len(inventory_data[inventory_data['stock_status'] == 'Critical'])}")
        ])
    
    # Display summary table
    if summary_data:
        summary_df = pd.DataFrame(summary_data, columns=['Metric', 'Value'])
        st.dataframe(summary_df, use_container_width=True, hide_index=True)
    
    # Export options
    st.subheader("📤 Export Options")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("### 📊 Excel Export")
        st.markdown("Export data to Excel spreadsheet with multiple sheets:")
        st.markdown("- Forecast results")
        st.markdown("- Inventory recommendations") 
        st.markdown("- Summary statistics")
        
        if st.button("📊 Generate Excel Report", type="primary"):
            with st.spinner("Generating Excel report..."):
                forecast_data = st.session_state.forecast_data if include_forecast else None
                inventory_data = st.session_state.inventory_data if include_inventory else None
                
                excel_content = export_to_excel(
                    forecast_data,
                    inventory_data,
                    filename=f"{report_title.replace(' ', '_')}.xlsx"
                )
            
            show_code_snippet(
                """
# Export to Excel
from utils.report_exporter import export_to_excel

excel_content = export_to_excel(
    forecast_data,
    inventory_data,
    filename="inventory_report.xlsx"
)

# Download the file
with open("inventory_report.xlsx", "wb") as f:
    f.write(excel_content)
                """,
                "Excel Export"
            )
            
            if excel_content:
                st.success("✅ Excel report generated successfully!")
                
                # Create download button
                st.download_button(
                    label="📥 Download Excel Report",
                    data=excel_content,
                    file_name=f"{report_title.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                )
            else:
                st.error("❌ Failed to generate Excel report.")
    
    with col2:
        st.markdown("### 📄 PDF Export")
        st.markdown("Export comprehensive PDF report with:")
        st.markdown("- Executive summary")
        st.markdown("- Data analysis results")
        st.markdown("- Charts and visualizations")
        st.markdown("- Recommendations")
        
        if st.button("📄 Generate PDF Report", type="primary"):
            with st.spinner("Generating PDF report..."):
                forecast_data = st.session_state.forecast_data if include_forecast else None
                inventory_data = st.session_state.inventory_data if include_inventory else None
                
                pdf_content = create_pdf_report(
                    forecast_data,
                    inventory_data,
                    filename=f"{report_title.replace(' ', '_')}.pdf"
                )
            
            show_code_snippet(
                """
# Export to PDF
from utils.report_exporter import create_pdf_report

pdf_content = create_pdf_report(
    forecast_data,
    inventory_data,
    filename="inventory_report.pdf"
)

# Download the file
with open("inventory_report.pdf", "wb") as f:
    f.write(pdf_content)
                """,
                "PDF Export"
            )
            
            if pdf_content:
                st.success("✅ PDF report generated successfully!")
                
                # Create download button
                st.download_button(
                    label="📥 Download PDF Report",
                    data=pdf_content,
                    file_name=f"{report_title.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d')}.pdf",
                    mime="application/pdf"
                )
            else:
                st.error("❌ Failed to generate PDF report.")
    
    # Data preview sections
    if has_forecast or has_inventory:
        st.subheader("👀 Data Preview")
        
        tab1, tab2, tab3 = st.tabs(["📈 Forecast Data", "📦 Inventory Data", "📊 Raw Data"])
        
        with tab1:
            if has_forecast:
                forecast_data = st.session_state.forecast_data
                st.markdown(f"**Forecast Data** ({len(forecast_data)} rows)")
                
                # Format dates for display
                display_forecast = forecast_data.copy()
                if 'ds' in display_forecast.columns:
                    display_forecast['ds'] = pd.to_datetime(display_forecast['ds']).dt.strftime('%Y-%m-%d')
                
                st.dataframe(display_forecast.head(20), use_container_width=True)
                
                if len(display_forecast) > 20:
                    st.info(f"Showing first 20 rows of {len(display_forecast)} forecast points.")
            else:
                st.info("No forecast data available. Generate a forecast first.")
        
        with tab2:
            if has_inventory:
                inventory_data = st.session_state.inventory_data
                st.markdown(f"**Inventory Data** ({len(inventory_data)} rows)")
                
                # Format dates for display
                display_inventory = inventory_data.copy()
                if 'ds' in display_inventory.columns:
                    display_inventory['ds'] = pd.to_datetime(display_inventory['ds']).dt.strftime('%Y-%m-%d')
                
                # Select key columns
                key_columns = [
                    'ds', 'predicted_demand', 'recommended_stock', 
                    'current_stock_level', 'order_quantity', 'stock_status'
                ]
                available_columns = [col for col in key_columns if col in display_inventory.columns]
                display_inventory = display_inventory[available_columns]
                
                st.dataframe(display_inventory.head(20), use_container_width=True)
                
                if len(display_inventory) > 20:
                    st.info(f"Showing first 20 rows of {len(display_inventory)} inventory recommendations.")
            else:
                st.info("No inventory data available. Generate inventory recommendations first.")
        
        with tab3:
            if has_cleaned_data:
                cleaned_data = st.session_state.cleaned_data
                st.markdown(f"**Cleaned Data** ({len(cleaned_data)} rows)")
                st.dataframe(cleaned_data.head(20), use_container_width=True)
                
                if len(cleaned_data) > 20:
                    st.info(f"Showing first 20 rows of {len(cleaned_data)} records.")
    
    # Custom export options
    st.subheader("🔧 Custom Export")
    
    with st.expander("Advanced Export Options"):
        st.markdown("**Custom Data Selection**")
        
        if has_forecast:
            export_forecast_cols = st.multiselect(
                "Forecast Columns to Export",
                options=list(st.session_state.forecast_data.columns),
                default=list(st.session_state.forecast_data.columns),
                help="Select which forecast columns to include in export"
            )
        
        if has_inventory:
            export_inventory_cols = st.multiselect(
                "Inventory Columns to Export",
                options=list(st.session_state.inventory_data.columns),
                default=[col for col in ['ds', 'predicted_demand', 'recommended_stock', 'order_quantity', 'stock_status'] 
                        if col in st.session_state.inventory_data.columns],
                help="Select which inventory columns to include in export"
            )
        
        # Date range filter
        if has_forecast or has_inventory:
            st.markdown("**Date Range Filter**")
            
            # Get date range from available data
            min_date = None
            max_date = None
            
            if has_forecast:
                forecast_dates = pd.to_datetime(st.session_state.forecast_data['ds'])
                min_date = forecast_dates.min().date()
                max_date = forecast_dates.max().date()
            
            if has_inventory:
                inventory_dates = pd.to_datetime(st.session_state.inventory_data['ds'])
                if min_date is None:
                    min_date = inventory_dates.min().date()
                    max_date = inventory_dates.max().date()
                else:
                    min_date = min(min_date, inventory_dates.min().date())
                    max_date = max(max_date, inventory_dates.max().date())
            
            if min_date and max_date:
                date_range = st.date_input(
                    "Export Date Range",
                    value=(min_date, max_date),
                    min_value=min_date,
                    max_value=max_date,
                    help="Select date range for export"
                )
                
                if len(date_range) == 2:
                    start_date, end_date = date_range
                    st.info(f"Export will include data from {start_date} to {end_date}")
        
        if st.button("📤 Export Custom Selection"):
            st.info("Custom export functionality can be implemented based on the selections above.")
    
    # Report scheduling (future feature)
    st.subheader("⏰ Automated Reports")
    
    with st.expander("Schedule Automated Reports (Coming Soon)"):
        st.info(
            "🚧 **Coming Soon**: Automated report generation and delivery\n\n"
            "Future features will include:\n"
            "- Scheduled daily/weekly/monthly reports\n"
            "- Email delivery of reports\n"
            "- Integration with cloud storage\n"
            "- Real-time dashboard updates"
        )
        
        # Placeholder for future scheduling options
        schedule_frequency = st.selectbox(
            "Report Frequency",
            options=["Daily", "Weekly", "Monthly"],
            disabled=True
        )
        
        email_recipients = st.text_input(
            "Email Recipients",
            placeholder="<EMAIL>, <EMAIL>",
            disabled=True
        )
    
    # Help section
    st.subheader("❓ Export Help")
    
    with st.expander("Export Formats & Usage"):
        st.markdown("""
        **Excel Format (.xlsx)**
        - Best for data analysis and manipulation
        - Multiple sheets for different data types
        - Formatted tables with conditional formatting
        - Compatible with Excel, Google Sheets, etc.
        
        **PDF Format (.pdf)**
        - Best for presentations and reports
        - Professional formatting with charts
        - Executive summary and recommendations
        - Print-ready format
        
        **Usage Tips:**
        - Excel files are ideal for further analysis
        - PDF files are perfect for sharing with stakeholders
        - Both formats include all selected data and analysis
        - Files are named with timestamp for version control
        """)
    
    # Footer
    st.markdown("---")
    st.markdown(
        f"*Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} "
        f"by Inventory Prediction System*"
    )
