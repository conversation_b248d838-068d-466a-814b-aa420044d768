"""
Forecasting Page for the Inventory Prediction System.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from utils.forecasting import (
    ProphetForecaster, ARIMAForecaster, create_forecast_plot, 
    run_forecast, evaluate_forecast_accuracy, PROPHET_AVAILABLE, ARIMA_AVAILABLE
)
from utils.data_cleaning import prepare_for_forecasting


def show_code_snippet(code, description):
    """Show code snippet if code view is enabled."""
    if st.session_state.show_code:
        with st.expander(f"💻 Code: {description}"):
            st.code(code, language='python')


def show_forecasting_page():
    """Display the forecasting page."""
    st.title("🔮 Forecasting")
    st.markdown("Generate demand forecasts using advanced machine learning models.")
    
    if not st.session_state.data_loaded or st.session_state.cleaned_data is None:
        st.warning("⚠️ Please upload and clean your data first in the 'Data Upload & Cleaning' section.")
        return
    
    df = st.session_state.cleaned_data
    column_mapping = st.session_state.column_mapping
    
    # Get mapped columns
    date_col = column_mapping.get('date')
    sales_col = column_mapping.get('sales')
    product_col = column_mapping.get('product')
    
    if not date_col or not sales_col:
        st.error("❌ Date and Sales columns are required for forecasting.")
        return
    
    # Forecasting configuration
    st.subheader("⚙️ Forecasting Configuration")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        forecast_periods = st.selectbox(
            "Forecast Period",
            options=[30, 60, 90, 120, 180, 365],
            index=0,
            help="Number of days to forecast into the future"
        )
    
    with col2:
        available_models = []
        if PROPHET_AVAILABLE:
            available_models.append("Prophet")
        if ARIMA_AVAILABLE:
            available_models.append("ARIMA")
        
        if not available_models:
            st.error("❌ No forecasting models available. Please install Prophet and/or ARIMA.")
            return
        
        model_type = st.selectbox(
            "Forecasting Model",
            options=available_models,
            help="Choose the machine learning model for forecasting"
        )
    
    with col3:
        aggregation_freq = st.selectbox(
            "Data Frequency",
            options=["D", "W", "M"],
            format_func=lambda x: {"D": "Daily", "W": "Weekly", "M": "Monthly"}[x],
            help="Frequency for data aggregation"
        )
    
    # Model-specific parameters
    st.subheader("🎛️ Model Parameters")
    
    model_params = {}
    
    if model_type == "Prophet":
        col1, col2 = st.columns(2)
        
        with col1:
            model_params['seasonality_mode'] = st.selectbox(
                "Seasonality Mode",
                options=['additive', 'multiplicative'],
                help="How seasonal effects combine with the trend"
            )
            
            model_params['yearly_seasonality'] = st.checkbox(
                "Yearly Seasonality",
                value=True,
                help="Include yearly seasonal patterns"
            )
        
        with col2:
            model_params['weekly_seasonality'] = st.checkbox(
                "Weekly Seasonality",
                value=True,
                help="Include weekly seasonal patterns"
            )
            
            model_params['daily_seasonality'] = st.checkbox(
                "Daily Seasonality",
                value=False,
                help="Include daily seasonal patterns"
            )
    
    elif model_type == "ARIMA":
        col1, col2 = st.columns(2)
        
        with col1:
            auto_order = st.checkbox(
                "Auto-detect ARIMA Order",
                value=True,
                help="Automatically find the best ARIMA parameters"
            )
        
        with col2:
            if not auto_order:
                p = st.number_input("AR Order (p)", min_value=0, max_value=5, value=1)
                d = st.number_input("Differencing (d)", min_value=0, max_value=2, value=1)
                q = st.number_input("MA Order (q)", min_value=0, max_value=5, value=1)
                model_params['order'] = (p, d, q)
                model_params['auto_order'] = False
            else:
                model_params['auto_order'] = True
    
    # Product selection (if multiple products)
    if product_col and df[product_col].nunique() > 1:
        st.subheader("🏷️ Product Selection")
        
        selected_product = st.selectbox(
            "Select Product for Forecasting",
            options=['All Products'] + list(df[product_col].unique()),
            help="Choose a specific product or forecast for all products combined"
        )
        
        if selected_product != 'All Products':
            df = df[df[product_col] == selected_product]
            st.info(f"Forecasting for: {selected_product}")
    
    # Prepare data for forecasting
    forecast_df = prepare_for_forecasting(df, date_col, sales_col, freq=aggregation_freq)
    
    # Show data preparation summary
    st.subheader("📊 Data Preparation Summary")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Original Records", len(df))
    
    with col2:
        st.metric("Prepared Records", len(forecast_df))
    
    with col3:
        date_range = (forecast_df['ds'].max() - forecast_df['ds'].min()).days
        st.metric("Date Range (Days)", date_range)
    
    with col4:
        st.metric("Average Daily Sales", f"{forecast_df['y'].mean():.1f}")
    
    # Show prepared data preview
    with st.expander("👀 View Prepared Data"):
        st.dataframe(forecast_df.head(10), use_container_width=True)
    
    # Generate forecast
    if st.button("🔮 Generate Forecast", type="primary"):
        if len(forecast_df) < 10:
            st.error("❌ Insufficient data for forecasting. Need at least 10 data points.")
            return
        
        with st.spinner(f"Training {model_type} model and generating forecast..."):
            forecast_result, model_obj, success = run_forecast(
                forecast_df,
                model_type=model_type,
                forecast_periods=forecast_periods,
                **model_params
            )
        
        show_code_snippet(
            f"""
# {model_type} Forecasting
from utils.forecasting import run_forecast
from utils.data_cleaning import prepare_for_forecasting

# Prepare data
forecast_df = prepare_for_forecasting(df, '{date_col}', '{sales_col}', freq='{aggregation_freq}')

# Generate forecast
forecast_result, model_obj, success = run_forecast(
    forecast_df,
    model_type='{model_type}',
    forecast_periods={forecast_periods},
    {', '.join([f'{k}={repr(v)}' for k, v in model_params.items()])}
)
            """,
            f"{model_type} Forecasting"
        )
        
        if success and forecast_result is not None:
            st.session_state.forecast_data = forecast_result
            
            st.success(f"✅ {model_type} forecast generated successfully!")
            
            # Display forecast results
            st.subheader("📈 Forecast Results")
            
            # Create and display forecast plot
            fig = create_forecast_plot(
                forecast_df,
                forecast_result,
                title=f"{model_type} Demand Forecast ({forecast_periods} days)"
            )
            st.plotly_chart(fig, use_container_width=True)
            
            # Forecast statistics
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                avg_forecast = forecast_result['yhat'].mean()
                st.metric("Avg Forecasted Demand", f"{avg_forecast:.1f}")
            
            with col2:
                total_forecast = forecast_result['yhat'].sum()
                st.metric("Total Forecasted Demand", f"{total_forecast:.0f}")
            
            with col3:
                max_forecast = forecast_result['yhat'].max()
                st.metric("Peak Demand", f"{max_forecast:.1f}")
            
            with col4:
                if 'yhat_upper' in forecast_result.columns and 'yhat_lower' in forecast_result.columns:
                    avg_uncertainty = (forecast_result['yhat_upper'] - forecast_result['yhat_lower']).mean()
                    st.metric("Avg Uncertainty", f"±{avg_uncertainty:.1f}")
            
            # Forecast table
            st.subheader("📋 Forecast Table")
            
            # Format forecast data for display
            display_forecast = forecast_result.copy()
            display_forecast['ds'] = pd.to_datetime(display_forecast['ds']).dt.strftime('%Y-%m-%d')
            
            # Round numerical columns
            numerical_cols = ['yhat', 'yhat_lower', 'yhat_upper']
            for col in numerical_cols:
                if col in display_forecast.columns:
                    display_forecast[col] = display_forecast[col].round(2)
            
            # Show first 20 rows
            st.dataframe(display_forecast.head(20), use_container_width=True)
            
            if len(display_forecast) > 20:
                st.info(f"Showing first 20 rows of {len(display_forecast)} forecast points. Full data available in reports.")
            
            # Model performance (if we have historical data to validate against)
            if len(forecast_df) > forecast_periods:
                st.subheader("📊 Model Performance")
                
                # Use last portion of historical data for validation
                validation_size = min(30, len(forecast_df) // 4)
                train_data = forecast_df.iloc[:-validation_size]
                validation_data = forecast_df.iloc[-validation_size:]
                
                # Generate validation forecast
                val_forecast, val_model, val_success = run_forecast(
                    train_data,
                    model_type=model_type,
                    forecast_periods=validation_size,
                    **model_params
                )
                
                if val_success and val_forecast is not None:
                    # Calculate accuracy metrics
                    actual_values = validation_data['y'].values
                    predicted_values = val_forecast['yhat'].values[:len(actual_values)]
                    
                    metrics = evaluate_forecast_accuracy(actual_values, predicted_values)
                    
                    col1, col2, col3, col4 = st.columns(4)
                    
                    with col1:
                        st.metric("MAE", f"{metrics.get('MAE', 0):.2f}")
                    
                    with col2:
                        st.metric("RMSE", f"{metrics.get('RMSE', 0):.2f}")
                    
                    with col3:
                        st.metric("MAPE", f"{metrics.get('MAPE', 0):.1f}%")
                    
                    with col4:
                        # Calculate accuracy percentage
                        accuracy = max(0, 100 - metrics.get('MAPE', 100))
                        st.metric("Accuracy", f"{accuracy:.1f}%")
                    
                    # Validation plot
                    fig_val = create_validation_plot(validation_data, val_forecast)
                    st.plotly_chart(fig_val, use_container_width=True)
            
            # Forecast insights
            st.subheader("💡 Forecast Insights")
            insights = generate_forecast_insights(forecast_result, forecast_df)
            
            for insight in insights:
                if insight['type'] == 'info':
                    st.info(f"ℹ️ {insight['message']}")
                elif insight['type'] == 'warning':
                    st.warning(f"⚠️ {insight['message']}")
                elif insight['type'] == 'success':
                    st.success(f"✅ {insight['message']}")
            
            # Next steps
            st.subheader("🎯 Next Steps")
            st.info(
                "Your forecast is ready! You can now:\n"
                "- Use this forecast for **Inventory Planning**\n"
                "- Export results in the **Reports & Export** section\n"
                "- Adjust model parameters and regenerate if needed"
            )
        
        else:
            st.error("❌ Failed to generate forecast. Please check your data and model parameters.")


def create_validation_plot(actual_data, forecast_data):
    """Create a validation plot comparing actual vs predicted values."""
    fig = go.Figure()
    
    # Actual values
    fig.add_trace(go.Scatter(
        x=actual_data['ds'],
        y=actual_data['y'],
        mode='lines+markers',
        name='Actual',
        line=dict(color='blue', width=2)
    ))
    
    # Predicted values
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'][:len(actual_data)],
        y=forecast_data['yhat'][:len(actual_data)],
        mode='lines+markers',
        name='Predicted',
        line=dict(color='red', width=2, dash='dash')
    ))
    
    fig.update_layout(
        title='Model Validation: Actual vs Predicted',
        xaxis_title='Date',
        yaxis_title='Sales',
        hovermode='x unified',
        template='plotly_white',
        height=400
    )
    
    return fig


def generate_forecast_insights(forecast_data, historical_data):
    """Generate insights based on the forecast results."""
    insights = []
    
    # Trend analysis
    forecast_trend = forecast_data['yhat'].iloc[-7:].mean() - forecast_data['yhat'].iloc[:7].mean()
    historical_avg = historical_data['y'].mean()
    forecast_avg = forecast_data['yhat'].mean()
    
    if forecast_avg > historical_avg * 1.1:
        insights.append({
            'type': 'success',
            'message': f"Forecast shows {((forecast_avg/historical_avg-1)*100):.1f}% increase in average demand compared to historical data."
        })
    elif forecast_avg < historical_avg * 0.9:
        insights.append({
            'type': 'warning',
            'message': f"Forecast shows {((1-forecast_avg/historical_avg)*100):.1f}% decrease in average demand compared to historical data."
        })
    
    # Volatility analysis
    forecast_std = forecast_data['yhat'].std()
    historical_std = historical_data['y'].std()
    
    if forecast_std > historical_std * 1.2:
        insights.append({
            'type': 'warning',
            'message': "Forecast shows higher volatility than historical data. Consider higher safety stock levels."
        })
    
    # Peak demand analysis
    peak_demand = forecast_data['yhat'].max()
    peak_date = forecast_data.loc[forecast_data['yhat'].idxmax(), 'ds']
    
    if peak_demand > forecast_avg * 1.5:
        insights.append({
            'type': 'info',
            'message': f"Peak demand of {peak_demand:.1f} expected around {pd.to_datetime(peak_date).strftime('%Y-%m-%d')}."
        })
    
    return insights
