"""
Exploratory Data Analysis Page for the Inventory Prediction System.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import seaborn as sns
import matplotlib.pyplot as plt
from datetime import datetime, <PERSON><PERSON><PERSON>


def show_code_snippet(code, description):
    """Show code snippet if code view is enabled."""
    if st.session_state.show_code:
        with st.expander(f"💻 Code: {description}"):
            st.code(code, language='python')


def show_eda_page():
    """Display the exploratory data analysis page."""
    st.title("📈 Exploratory Data Analysis")
    st.markdown("Explore your data patterns and gain insights for better forecasting.")
    
    if not st.session_state.data_loaded or st.session_state.cleaned_data is None:
        st.warning("⚠️ Please upload and clean your data first in the 'Data Upload & Cleaning' section.")
        return
    
    df = st.session_state.cleaned_data
    column_mapping = st.session_state.column_mapping
    
    # Get mapped columns
    date_col = column_mapping.get('date')
    sales_col = column_mapping.get('sales')
    product_col = column_mapping.get('product')
    store_col = column_mapping.get('store')
    
    if not date_col or not sales_col:
        st.error("❌ Date and Sales columns are required for analysis.")
        return
    
    # Data overview section
    st.subheader("📊 Data Overview")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_sales = df[sales_col].sum()
        st.metric("Total Sales", f"{total_sales:,.0f}")
    
    with col2:
        avg_daily_sales = df.groupby(date_col)[sales_col].sum().mean()
        st.metric("Avg Daily Sales", f"{avg_daily_sales:.1f}")
    
    with col3:
        date_range = (df[date_col].max() - df[date_col].min()).days
        st.metric("Date Range (Days)", date_range)
    
    with col4:
        if product_col:
            unique_products = df[product_col].nunique()
            st.metric("Unique Products", unique_products)
        else:
            st.metric("Records", len(df))
    
    # Time series analysis
    st.subheader("📈 Time Series Analysis")
    
    # Daily sales trend
    daily_sales = df.groupby(date_col)[sales_col].sum().reset_index()
    
    fig_trend = px.line(
        daily_sales,
        x=date_col,
        y=sales_col,
        title="Daily Sales Trend",
        labels={date_col: 'Date', sales_col: 'Sales'}
    )
    
    fig_trend.update_layout(height=500)
    st.plotly_chart(fig_trend, use_container_width=True)
    
    show_code_snippet(
        f"""
# Daily sales trend analysis
import plotly.express as px

daily_sales = df.groupby('{date_col}')['{sales_col}'].sum().reset_index()

fig = px.line(
    daily_sales,
    x='{date_col}',
    y='{sales_col}',
    title="Daily Sales Trend"
)
fig.show()
        """,
        "Time Series Trend"
    )
    
    # Seasonal patterns
    st.subheader("🔄 Seasonal Patterns")
    
    # Add time-based features
    df_time = daily_sales.copy()
    df_time['year'] = df_time[date_col].dt.year
    df_time['month'] = df_time[date_col].dt.month
    df_time['day_of_week'] = df_time[date_col].dt.day_name()
    df_time['day_of_month'] = df_time[date_col].dt.day
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Monthly pattern
        monthly_sales = df_time.groupby('month')[sales_col].mean().reset_index()
        monthly_sales['month_name'] = pd.to_datetime(monthly_sales['month'], format='%m').dt.month_name()
        
        fig_monthly = px.bar(
            monthly_sales,
            x='month_name',
            y=sales_col,
            title="Average Sales by Month",
            labels={'month_name': 'Month', sales_col: 'Average Sales'}
        )
        st.plotly_chart(fig_monthly, use_container_width=True)
    
    with col2:
        # Day of week pattern
        dow_sales = df_time.groupby('day_of_week')[sales_col].mean().reset_index()
        # Reorder days
        day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        dow_sales['day_of_week'] = pd.Categorical(dow_sales['day_of_week'], categories=day_order, ordered=True)
        dow_sales = dow_sales.sort_values('day_of_week')
        
        fig_dow = px.bar(
            dow_sales,
            x='day_of_week',
            y=sales_col,
            title="Average Sales by Day of Week",
            labels={'day_of_week': 'Day of Week', sales_col: 'Average Sales'}
        )
        st.plotly_chart(fig_dow, use_container_width=True)
    
    # Product analysis (if product column exists)
    if product_col:
        st.subheader("🏷️ Product Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Top products
            top_products = df.groupby(product_col)[sales_col].sum().sort_values(ascending=False).head(10)
            
            fig_top_products = px.bar(
                x=top_products.values,
                y=top_products.index,
                orientation='h',
                title="Top 10 Products by Total Sales",
                labels={'x': 'Total Sales', 'y': 'Product'}
            )
            fig_top_products.update_layout(height=400)
            st.plotly_chart(fig_top_products, use_container_width=True)
        
        with col2:
            # Product performance over time
            product_time = df.groupby([date_col, product_col])[sales_col].sum().reset_index()
            top_5_products = top_products.head(5).index.tolist()
            product_time_filtered = product_time[product_time[product_col].isin(top_5_products)]
            
            fig_product_time = px.line(
                product_time_filtered,
                x=date_col,
                y=sales_col,
                color=product_col,
                title="Top 5 Products Sales Over Time",
                labels={date_col: 'Date', sales_col: 'Sales'}
            )
            fig_product_time.update_layout(height=400)
            st.plotly_chart(fig_product_time, use_container_width=True)
    
    # Store analysis (if store column exists)
    if store_col:
        st.subheader("🏪 Store Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Store performance
            store_sales = df.groupby(store_col)[sales_col].sum().sort_values(ascending=False)
            
            fig_store = px.bar(
                x=store_sales.index,
                y=store_sales.values,
                title="Total Sales by Store",
                labels={'x': 'Store', 'y': 'Total Sales'}
            )
            st.plotly_chart(fig_store, use_container_width=True)
        
        with col2:
            # Store sales distribution
            fig_store_dist = px.box(
                df,
                x=store_col,
                y=sales_col,
                title="Sales Distribution by Store"
            )
            st.plotly_chart(fig_store_dist, use_container_width=True)
    
    # Statistical analysis
    st.subheader("📊 Statistical Analysis")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Sales distribution
        fig_hist = px.histogram(
            daily_sales,
            x=sales_col,
            nbins=30,
            title="Daily Sales Distribution",
            labels={sales_col: 'Daily Sales', 'count': 'Frequency'}
        )
        st.plotly_chart(fig_hist, use_container_width=True)
    
    with col2:
        # Sales statistics
        st.markdown("**Sales Statistics**")
        stats_df = pd.DataFrame({
            'Metric': ['Mean', 'Median', 'Std Dev', 'Min', 'Max', 'Skewness', 'Kurtosis'],
            'Value': [
                f"{daily_sales[sales_col].mean():.2f}",
                f"{daily_sales[sales_col].median():.2f}",
                f"{daily_sales[sales_col].std():.2f}",
                f"{daily_sales[sales_col].min():.2f}",
                f"{daily_sales[sales_col].max():.2f}",
                f"{daily_sales[sales_col].skew():.2f}",
                f"{daily_sales[sales_col].kurtosis():.2f}"
            ]
        })
        st.dataframe(stats_df, use_container_width=True)
    
    # Correlation analysis
    st.subheader("🔗 Correlation Analysis")
    
    # Create correlation matrix with time features
    corr_df = df_time[[sales_col, 'year', 'month', 'day_of_month']].copy()
    
    # Add lag features
    corr_df['sales_lag_1'] = corr_df[sales_col].shift(1)
    corr_df['sales_lag_7'] = corr_df[sales_col].shift(7)
    corr_df['sales_ma_7'] = corr_df[sales_col].rolling(window=7).mean()
    
    corr_matrix = corr_df.corr()
    
    fig_corr = px.imshow(
        corr_matrix,
        text_auto=True,
        aspect="auto",
        title="Correlation Matrix",
        color_continuous_scale='RdBu_r'
    )
    st.plotly_chart(fig_corr, use_container_width=True)
    
    # Insights and recommendations
    st.subheader("💡 Key Insights")
    
    insights = generate_insights(daily_sales, df, sales_col, date_col, product_col, store_col)
    
    for insight in insights:
        if insight['type'] == 'info':
            st.info(f"ℹ️ {insight['message']}")
        elif insight['type'] == 'warning':
            st.warning(f"⚠️ {insight['message']}")
        elif insight['type'] == 'success':
            st.success(f"✅ {insight['message']}")
    
    # Next steps
    st.subheader("🎯 Next Steps")
    st.info(
        "Based on your data analysis, you can now:\n"
        "- Generate forecasts using the **Forecasting** section\n"
        "- Consider seasonal patterns when setting inventory levels\n"
        "- Focus on top-performing products for inventory optimization"
    )


def generate_insights(daily_sales, df, sales_col, date_col, product_col, store_col):
    """Generate insights based on the data analysis."""
    insights = []
    
    # Trend analysis
    recent_trend = daily_sales[sales_col].tail(30).mean()
    overall_trend = daily_sales[sales_col].mean()
    
    if recent_trend > overall_trend * 1.1:
        insights.append({
            'type': 'success',
            'message': f"Sales are trending upward! Recent 30-day average ({recent_trend:.1f}) is {((recent_trend/overall_trend-1)*100):.1f}% higher than overall average."
        })
    elif recent_trend < overall_trend * 0.9:
        insights.append({
            'type': 'warning',
            'message': f"Sales are trending downward. Recent 30-day average ({recent_trend:.1f}) is {((1-recent_trend/overall_trend)*100):.1f}% lower than overall average."
        })
    
    # Volatility analysis
    cv = daily_sales[sales_col].std() / daily_sales[sales_col].mean()
    if cv > 0.5:
        insights.append({
            'type': 'warning',
            'message': f"High sales volatility detected (CV: {cv:.2f}). Consider higher safety stock levels."
        })
    elif cv < 0.2:
        insights.append({
            'type': 'success',
            'message': f"Low sales volatility (CV: {cv:.2f}). Demand is relatively stable and predictable."
        })
    
    # Seasonality detection
    if len(daily_sales) > 365:
        monthly_cv = daily_sales.groupby(daily_sales[date_col].dt.month)[sales_col].mean().std() / daily_sales.groupby(daily_sales[date_col].dt.month)[sales_col].mean().mean()
        if monthly_cv > 0.3:
            insights.append({
                'type': 'info',
                'message': f"Strong seasonal patterns detected. Consider seasonal forecasting models."
            })
    
    # Product concentration (if applicable)
    if product_col:
        product_sales = df.groupby(product_col)[sales_col].sum().sort_values(ascending=False)
        top_20_pct = product_sales.head(int(len(product_sales) * 0.2)).sum() / product_sales.sum()
        
        if top_20_pct > 0.8:
            insights.append({
                'type': 'warning',
                'message': f"High product concentration: Top 20% of products account for {top_20_pct:.1%} of sales. Focus inventory management on key products."
            })
    
    return insights
