"""
Data Upload and Cleaning Page for the Inventory Prediction System.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from utils.data_cleaning import (
    load_data, clean_data, validate_data_structure, 
    get_column_info, detect_date_column
)


def show_code_snippet(code, description):
    """Show code snippet if code view is enabled."""
    if st.session_state.show_code:
        with st.expander(f"💻 Code: {description}"):
            st.code(code, language='python')


def show_data_upload_page():
    """Display the data upload and cleaning page."""
    st.title("📊 Data Upload & Cleaning")
    st.markdown("Upload your historical sales data and prepare it for analysis.")
    
    # File upload section
    st.subheader("📁 Upload Data File")
    
    uploaded_file = st.file_uploader(
        "Choose a CSV or Excel file",
        type=['csv', 'xlsx', 'xls'],
        help="Upload your historical sales data with columns like Date, Product, Sales, Store, etc."
    )
    
    if uploaded_file is not None:
        # Load data
        with st.spinner("Loading data..."):
            raw_data = load_data(uploaded_file)
        
        show_code_snippet(
            f"""
# Load data from uploaded file
import pandas as pd

# For CSV files
df = pd.read_csv('{uploaded_file.name}')

# For Excel files  
df = pd.read_excel('{uploaded_file.name}')

print(f"Data shape: {{df.shape}}")
            """,
            "Data Loading"
        )
        
        if raw_data is not None:
            st.session_state.data_loaded = True
            
            # Display raw data info
            st.subheader("📋 Raw Data Overview")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Rows", len(raw_data))
            with col2:
                st.metric("Columns", len(raw_data.columns))
            with col3:
                st.metric("Missing Values", raw_data.isnull().sum().sum())
            
            # Show sample data
            st.subheader("👀 Data Preview")
            st.dataframe(raw_data.head(10), use_container_width=True)
            
            # Column information
            st.subheader("📊 Column Information")
            column_info = get_column_info(raw_data)
            
            info_data = []
            for col, info in column_info.items():
                info_data.append({
                    'Column': col,
                    'Data Type': info['dtype'],
                    'Null Count': info['null_count'],
                    'Unique Values': info['unique_count'],
                    'Sample Values': ', '.join(map(str, info['sample_values'][:3]))
                })
            
            info_df = pd.DataFrame(info_data)
            st.dataframe(info_df, use_container_width=True)
            
            # Column mapping section
            st.subheader("🔗 Column Mapping")
            st.markdown("Map your columns to the required fields for analysis:")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Auto-detect date column
                detected_date = detect_date_column(raw_data)
                date_col = st.selectbox(
                    "Date Column",
                    options=[None] + list(raw_data.columns),
                    index=list(raw_data.columns).index(detected_date) + 1 if detected_date else 0,
                    help="Select the column containing dates"
                )
                
                sales_col = st.selectbox(
                    "Sales/Demand Column",
                    options=[None] + list(raw_data.select_dtypes(include=['number']).columns),
                    help="Select the column containing sales quantities or demand values"
                )
            
            with col2:
                product_col = st.selectbox(
                    "Product Column (Optional)",
                    options=[None] + list(raw_data.columns),
                    help="Select the column containing product identifiers"
                )
                
                store_col = st.selectbox(
                    "Store Column (Optional)",
                    options=[None] + list(raw_data.columns),
                    help="Select the column containing store or location identifiers"
                )
            
            # Store column mapping
            st.session_state.column_mapping = {
                'date': date_col,
                'sales': sales_col,
                'product': product_col,
                'store': store_col
            }
            
            # Data cleaning section
            if date_col and sales_col:
                st.subheader("🧹 Data Cleaning")
                
                # Cleaning options
                col1, col2 = st.columns(2)
                
                with col1:
                    remove_negatives = st.checkbox("Remove negative sales values", value=True)
                    fill_missing = st.checkbox("Fill missing values", value=True)
                
                with col2:
                    remove_duplicates = st.checkbox("Remove duplicate rows", value=True)
                    sort_by_date = st.checkbox("Sort by date", value=True)
                
                if st.button("🧹 Clean Data", type="primary"):
                    with st.spinner("Cleaning data..."):
                        cleaned_data, cleaning_summary = clean_data(
                            raw_data,
                            date_col=date_col,
                            product_col=product_col,
                            sales_col=sales_col,
                            store_col=store_col
                        )
                    
                    show_code_snippet(
                        f"""
# Data cleaning process
import pandas as pd
from datetime import datetime

# Parse date column
df['{date_col}'] = pd.to_datetime(df['{date_col}'])

# Handle missing values
numerical_cols = df.select_dtypes(include=['number']).columns
for col in numerical_cols:
    df[col].fillna(df[col].median(), inplace=True)

# Remove negative sales
df = df[df['{sales_col}'] >= 0]

# Remove duplicates
df.drop_duplicates(inplace=True)

# Sort by date
df.sort_values(by='{date_col}', inplace=True)
                        """,
                        "Data Cleaning"
                    )
                    
                    if cleaned_data is not None:
                        st.session_state.cleaned_data = cleaned_data
                        
                        # Show cleaning summary
                        st.success("✅ Data cleaned successfully!")
                        
                        col1, col2, col3, col4 = st.columns(4)
                        
                        with col1:
                            st.metric(
                                "Rows After Cleaning",
                                cleaning_summary.get('final_rows', 0),
                                delta=-(cleaning_summary.get('rows_removed', 0))
                            )
                        
                        with col2:
                            st.metric(
                                "Missing Values Filled",
                                cleaning_summary.get('missing_values_filled', 0)
                            )
                        
                        with col3:
                            st.metric(
                                "Duplicates Removed",
                                cleaning_summary.get('duplicates_removed', 0)
                            )
                        
                        with col4:
                            st.metric(
                                "Negative Sales Removed",
                                cleaning_summary.get('negative_sales_removed', 0)
                            )
                        
                        # Show cleaned data preview
                        st.subheader("✨ Cleaned Data Preview")
                        st.dataframe(cleaned_data.head(10), use_container_width=True)
                        
                        # Data validation
                        st.subheader("✅ Data Validation")
                        required_columns = [col for col in [date_col, sales_col] if col]
                        is_valid, validation_messages = validate_data_structure(cleaned_data, required_columns)
                        
                        for message in validation_messages:
                            if "❌" in message:
                                st.error(message)
                            elif "⚠️" in message:
                                st.warning(message)
                            else:
                                st.success(message)
                        
                        # Data quality visualization
                        if is_valid:
                            st.subheader("📊 Data Quality Overview")
                            
                            # Create data quality charts
                            fig_missing = create_missing_values_chart(cleaned_data)
                            st.plotly_chart(fig_missing, use_container_width=True)
                            
                            if date_col in cleaned_data.columns:
                                fig_timeline = create_data_timeline_chart(cleaned_data, date_col, sales_col)
                                st.plotly_chart(fig_timeline, use_container_width=True)
                        
                        # Next steps
                        st.subheader("🎯 Next Steps")
                        st.info(
                            "✅ Data is ready for analysis! You can now:\n"
                            "- Explore your data in the **Exploratory Data Analysis** section\n"
                            "- Generate forecasts in the **Forecasting** section\n"
                            "- Plan inventory in the **Inventory Planning** section"
                        )
            
            else:
                st.warning("⚠️ Please select at least Date and Sales columns to proceed with cleaning.")
    
    else:
        st.info("👆 Please upload a CSV or Excel file to get started.")
        
        # Show sample data format
        st.subheader("📋 Expected Data Format")
        st.markdown("Your data should look something like this:")
        
        sample_data = pd.DataFrame({
            'Date': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04'],
            'Product': ['Product A', 'Product B', 'Product A', 'Product C'],
            'Sales': [100, 150, 120, 80],
            'Store': ['Store 1', 'Store 1', 'Store 2', 'Store 1'],
            'Price': [10.99, 15.50, 10.99, 12.00]
        })
        
        st.dataframe(sample_data, use_container_width=True)


def create_missing_values_chart(df):
    """Create a chart showing missing values by column."""
    missing_data = df.isnull().sum()
    missing_data = missing_data[missing_data > 0]
    
    if len(missing_data) == 0:
        # No missing values
        fig = go.Figure()
        fig.add_annotation(
            text="No missing values found! 🎉",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False,
            font=dict(size=20, color="green")
        )
        fig.update_layout(
            title="Missing Values Analysis",
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            height=300
        )
    else:
        fig = px.bar(
            x=missing_data.index,
            y=missing_data.values,
            title="Missing Values by Column",
            labels={'x': 'Column', 'y': 'Missing Count'}
        )
        fig.update_layout(height=300)
    
    return fig


def create_data_timeline_chart(df, date_col, sales_col):
    """Create a timeline chart of the data."""
    try:
        # Aggregate by date
        daily_data = df.groupby(date_col)[sales_col].sum().reset_index()

        fig = px.line(
            daily_data,
            x=date_col,
            y=sales_col,
            title="Sales Timeline",
            labels={date_col: 'Date', sales_col: 'Total Sales'}
        )

        fig.update_layout(height=400)
        return fig
    except Exception as e:
        st.error(f"Error creating timeline chart: {str(e)}")
        return go.Figure()
