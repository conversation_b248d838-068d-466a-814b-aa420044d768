"""
Report export utilities for the Inventory Prediction System.
This module handles Excel and PDF export functionality.
"""

import pandas as pd
import numpy as np
import streamlit as st
import io
import base64
from datetime import datetime
import tempfile
import os

try:
    from reportlab.lib import colors
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.linecharts import HorizontalLineChart
    from reportlab.graphics.charts.lineplots import LinePlot
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    st.warning("ReportLab not available for PDF export. Please install: pip install reportlab")


def export_to_excel(forecast_data, inventory_data, filename="inventory_forecast_report.xlsx"):
    """
    Export forecast and inventory data to Excel file.
    
    Args:
        forecast_data (pandas.DataFrame): Forecast results
        inventory_data (pandas.DataFrame): Inventory recommendations
        filename (str): Output filename
        
    Returns:
        bytes: Excel file content
    """
    try:
        # Create a BytesIO buffer
        buffer = io.BytesIO()
        
        # Create Excel writer
        with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
            # Write forecast data
            if forecast_data is not None and not forecast_data.empty:
                forecast_export = forecast_data.copy()
                if 'ds' in forecast_export.columns:
                    forecast_export['ds'] = pd.to_datetime(forecast_export['ds']).dt.strftime('%Y-%m-%d')
                forecast_export.to_excel(writer, sheet_name='Forecast', index=False)
            
            # Write inventory data
            if inventory_data is not None and not inventory_data.empty:
                inventory_export = inventory_data.copy()
                if 'ds' in inventory_export.columns:
                    inventory_export['ds'] = pd.to_datetime(inventory_export['ds']).dt.strftime('%Y-%m-%d')
                inventory_export.to_excel(writer, sheet_name='Inventory_Plan', index=False)
            
            # Create summary sheet
            summary_data = create_summary_data(forecast_data, inventory_data)
            summary_df = pd.DataFrame(list(summary_data.items()), columns=['Metric', 'Value'])
            summary_df.to_excel(writer, sheet_name='Summary', index=False)
            
            # Format the Excel file
            format_excel_sheets(writer, forecast_data, inventory_data)
        
        buffer.seek(0)
        return buffer.getvalue()
    
    except Exception as e:
        st.error(f"Error creating Excel file: {str(e)}")
        return None


def format_excel_sheets(writer, forecast_data, inventory_data):
    """
    Format Excel sheets with styling.
    
    Args:
        writer: Excel writer object
        forecast_data (pandas.DataFrame): Forecast data
        inventory_data (pandas.DataFrame): Inventory data
    """
    try:
        from openpyxl.styles import Font, PatternFill, Alignment
        from openpyxl.utils.dataframe import dataframe_to_rows
        
        workbook = writer.book
        
        # Format forecast sheet
        if 'Forecast' in workbook.sheetnames:
            ws_forecast = workbook['Forecast']
            
            # Header formatting
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            for cell in ws_forecast[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # Auto-adjust column widths
            for column in ws_forecast.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 20)
                ws_forecast.column_dimensions[column_letter].width = adjusted_width
        
        # Format inventory sheet similarly
        if 'Inventory_Plan' in workbook.sheetnames:
            ws_inventory = workbook['Inventory_Plan']
            
            for cell in ws_inventory[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center")
            
            # Highlight critical stock status
            if inventory_data is not None and 'stock_status' in inventory_data.columns:
                status_col_idx = list(inventory_data.columns).index('stock_status') + 1
                for row_idx, status in enumerate(inventory_data['stock_status'], start=2):
                    cell = ws_inventory.cell(row=row_idx, column=status_col_idx)
                    if status == 'Critical':
                        cell.fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")
                        cell.font = Font(color="FFFFFF", bold=True)
                    elif status == 'Low':
                        cell.fill = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid")
    
    except Exception as e:
        st.warning(f"Could not apply Excel formatting: {str(e)}")


def create_summary_data(forecast_data, inventory_data):
    """
    Create summary statistics for the report.
    
    Args:
        forecast_data (pandas.DataFrame): Forecast data
        inventory_data (pandas.DataFrame): Inventory data
        
    Returns:
        dict: Summary statistics
    """
    summary = {}
    
    # Report metadata
    summary['Report Generated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    summary['Forecast Period'] = f"{len(forecast_data)} days" if forecast_data is not None else "N/A"
    
    # Forecast statistics
    if forecast_data is not None and not forecast_data.empty:
        summary['Average Daily Demand'] = f"{forecast_data['yhat'].mean():.2f}"
        summary['Peak Demand'] = f"{forecast_data['yhat'].max():.2f}"
        summary['Total Forecasted Demand'] = f"{forecast_data['yhat'].sum():.2f}"
        
        if 'yhat_lower' in forecast_data.columns and 'yhat_upper' in forecast_data.columns:
            avg_uncertainty = (forecast_data['yhat_upper'] - forecast_data['yhat_lower']).mean()
            summary['Average Forecast Uncertainty'] = f"±{avg_uncertainty:.2f}"
    
    # Inventory statistics
    if inventory_data is not None and not inventory_data.empty:
        total_orders = inventory_data['order_quantity'].sum()
        summary['Total Recommended Orders'] = f"{total_orders:.2f}"
        
        reorder_days = len(inventory_data[inventory_data['order_quantity'] > 0])
        summary['Days Requiring Reorder'] = f"{reorder_days}"
        
        if 'stock_status' in inventory_data.columns:
            critical_days = len(inventory_data[inventory_data['stock_status'] == 'Critical'])
            summary['Critical Stock Days'] = f"{critical_days}"
            
            low_days = len(inventory_data[inventory_data['stock_status'] == 'Low'])
            summary['Low Stock Days'] = f"{low_days}"
    
    return summary


def create_pdf_report(forecast_data, inventory_data, charts_data=None, filename="inventory_report.pdf"):
    """
    Create a comprehensive PDF report.
    
    Args:
        forecast_data (pandas.DataFrame): Forecast results
        inventory_data (pandas.DataFrame): Inventory recommendations
        charts_data (dict): Chart images as base64 strings
        filename (str): Output filename
        
    Returns:
        bytes: PDF file content
    """
    if not REPORTLAB_AVAILABLE:
        st.error("ReportLab is required for PDF export")
        return None
    
    try:
        # Create a BytesIO buffer
        buffer = io.BytesIO()
        
        # Create PDF document
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        story = []
        
        # Get styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        
        # Title
        title = Paragraph("Inventory Prediction Report", title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Executive Summary
        story.append(Paragraph("Executive Summary", styles['Heading2']))
        summary_data = create_summary_data(forecast_data, inventory_data)
        
        summary_table_data = [['Metric', 'Value']]
        for key, value in summary_data.items():
            summary_table_data.append([key, str(value)])
        
        summary_table = Table(summary_table_data)
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
        # Forecast Results
        if forecast_data is not None and not forecast_data.empty:
            story.append(Paragraph("Forecast Results", styles['Heading2']))
            
            # Create forecast table (first 10 rows)
            forecast_display = forecast_data.head(10).copy()
            if 'ds' in forecast_display.columns:
                forecast_display['ds'] = pd.to_datetime(forecast_display['ds']).dt.strftime('%Y-%m-%d')
            
            forecast_table_data = [list(forecast_display.columns)]
            for _, row in forecast_display.iterrows():
                forecast_table_data.append([f"{val:.2f}" if isinstance(val, (int, float)) else str(val) 
                                          for val in row])
            
            forecast_table = Table(forecast_table_data)
            forecast_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(forecast_table)
            story.append(Spacer(1, 20))
        
        # Inventory Recommendations
        if inventory_data is not None and not inventory_data.empty:
            story.append(Paragraph("Inventory Recommendations", styles['Heading2']))
            
            # Create inventory table (first 10 rows)
            inventory_display = inventory_data.head(10).copy()
            if 'ds' in inventory_display.columns:
                inventory_display['ds'] = pd.to_datetime(inventory_display['ds']).dt.strftime('%Y-%m-%d')
            
            # Select key columns for display
            key_columns = ['ds', 'predicted_demand', 'recommended_stock', 'order_quantity', 'stock_status']
            available_columns = [col for col in key_columns if col in inventory_display.columns]
            inventory_display = inventory_display[available_columns]
            
            inventory_table_data = [list(inventory_display.columns)]
            for _, row in inventory_display.iterrows():
                inventory_table_data.append([f"{val:.2f}" if isinstance(val, (int, float)) else str(val) 
                                           for val in row])
            
            inventory_table = Table(inventory_table_data)
            inventory_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(inventory_table)
            story.append(Spacer(1, 20))
        
        # Recommendations section
        story.append(Paragraph("Key Recommendations", styles['Heading2']))
        recommendations = generate_recommendations(forecast_data, inventory_data)
        
        for rec in recommendations:
            story.append(Paragraph(f"• {rec}", styles['Normal']))
        
        story.append(Spacer(1, 20))
        
        # Footer
        footer_text = f"Report generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} by Inventory Prediction System"
        story.append(Paragraph(footer_text, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        buffer.seek(0)
        return buffer.getvalue()
    
    except Exception as e:
        st.error(f"Error creating PDF report: {str(e)}")
        return None


def generate_recommendations(forecast_data, inventory_data):
    """
    Generate key recommendations based on the analysis.
    
    Args:
        forecast_data (pandas.DataFrame): Forecast data
        inventory_data (pandas.DataFrame): Inventory data
        
    Returns:
        list: List of recommendation strings
    """
    recommendations = []
    
    if forecast_data is not None and not forecast_data.empty:
        avg_demand = forecast_data['yhat'].mean()
        peak_demand = forecast_data['yhat'].max()
        
        if peak_demand > avg_demand * 1.5:
            recommendations.append(f"Prepare for peak demand periods with up to {peak_demand:.0f} units per day")
        
        demand_trend = forecast_data['yhat'].iloc[-7:].mean() - forecast_data['yhat'].iloc[:7].mean()
        if demand_trend > 0:
            recommendations.append("Demand is trending upward - consider increasing base stock levels")
        elif demand_trend < 0:
            recommendations.append("Demand is trending downward - monitor for potential overstock")
    
    if inventory_data is not None and not inventory_data.empty:
        total_orders = inventory_data['order_quantity'].sum()
        if total_orders > 0:
            recommendations.append(f"Plan for {total_orders:.0f} total units to be ordered over the forecast period")
        
        if 'stock_status' in inventory_data.columns:
            critical_days = len(inventory_data[inventory_data['stock_status'] == 'Critical'])
            if critical_days > 0:
                recommendations.append(f"Address {critical_days} critical stock situations immediately")
            
            reorder_days = len(inventory_data[inventory_data['order_quantity'] > 0])
            if reorder_days > 0:
                recommendations.append(f"Schedule reorders for {reorder_days} days in the forecast period")
    
    if not recommendations:
        recommendations.append("Current inventory levels appear adequate for the forecast period")
    
    return recommendations


def create_download_link(file_content, filename, file_type="Excel"):
    """
    Create a download link for the file.
    
    Args:
        file_content (bytes): File content
        filename (str): Filename
        file_type (str): File type for display
        
    Returns:
        str: HTML download link
    """
    if file_content is None:
        return None
    
    b64 = base64.b64encode(file_content).decode()
    
    if file_type == "Excel":
        mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    elif file_type == "PDF":
        mime_type = "application/pdf"
    else:
        mime_type = "application/octet-stream"
    
    href = f'<a href="data:{mime_type};base64,{b64}" download="{filename}">Download {file_type} Report</a>'
    return href
