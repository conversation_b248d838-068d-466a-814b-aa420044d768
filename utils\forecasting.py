"""
Forecasting utilities for the Inventory Prediction System.
This module implements Prophet and ARIMA models for demand forecasting.
"""

import pandas as pd
import numpy as np
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    st.warning("Prophet not available. Please install: pip install prophet")

try:
    from statsmodels.tsa.arima.model import ARIMA
    from statsmodels.tsa.seasonal import seasonal_decompose
    from statsmodels.tsa.stattools import adfuller
    ARIMA_AVAILABLE = True
except ImportError:
    ARIMA_AVAILABLE = False
    st.warning("ARIMA not available. Please install: pip install statsmodels")


class ProphetForecaster:
    """Prophet-based forecasting model."""
    
    def __init__(self):
        self.model = None
        self.forecast = None
        self.is_fitted = False
    
    def fit(self, df, seasonality_mode='additive', yearly_seasonality=True, 
            weekly_seasonality=True, daily_seasonality=False):
        """
        Fit Prophet model to the data.
        
        Args:
            df (pandas.DataFrame): Data with 'ds' and 'y' columns
            seasonality_mode (str): 'additive' or 'multiplicative'
            yearly_seasonality (bool): Include yearly seasonality
            weekly_seasonality (bool): Include weekly seasonality
            daily_seasonality (bool): Include daily seasonality
        """
        if not PROPHET_AVAILABLE:
            raise ImportError("Prophet is not available")
        
        try:
            self.model = Prophet(
                seasonality_mode=seasonality_mode,
                yearly_seasonality=yearly_seasonality,
                weekly_seasonality=weekly_seasonality,
                daily_seasonality=daily_seasonality,
                interval_width=0.95
            )
            
            # Suppress Prophet's verbose output
            with st.spinner("Training Prophet model..."):
                self.model.fit(df)
            
            self.is_fitted = True
            return True
            
        except Exception as e:
            st.error(f"Error fitting Prophet model: {str(e)}")
            return False
    
    def predict(self, periods=30, freq='D'):
        """
        Generate forecasts.
        
        Args:
            periods (int): Number of periods to forecast
            freq (str): Frequency ('D', 'W', 'M')
            
        Returns:
            pandas.DataFrame: Forecast results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        try:
            future = self.model.make_future_dataframe(periods=periods, freq=freq)
            self.forecast = self.model.predict(future)
            
            # Return only the forecast period
            forecast_only = self.forecast.tail(periods).copy()
            forecast_only['model'] = 'Prophet'
            
            return forecast_only
            
        except Exception as e:
            st.error(f"Error generating Prophet forecast: {str(e)}")
            return None
    
    def get_components(self):
        """Get forecast components (trend, seasonality)."""
        if self.forecast is not None:
            return self.model.predict(self.forecast[['ds']])
        return None


class ARIMAForecaster:
    """ARIMA-based forecasting model."""
    
    def __init__(self):
        self.model = None
        self.fitted_model = None
        self.is_fitted = False
        self.order = None
    
    def find_best_order(self, df, max_p=3, max_d=2, max_q=3):
        """
        Find the best ARIMA order using AIC.
        
        Args:
            df (pandas.DataFrame): Data with 'ds' and 'y' columns
            max_p, max_d, max_q (int): Maximum values for p, d, q parameters
            
        Returns:
            tuple: Best (p, d, q) order
        """
        if not ARIMA_AVAILABLE:
            raise ImportError("ARIMA is not available")
        
        best_aic = float('inf')
        best_order = (1, 1, 1)
        
        with st.spinner("Finding optimal ARIMA parameters..."):
            for p in range(max_p + 1):
                for d in range(max_d + 1):
                    for q in range(max_q + 1):
                        try:
                            model = ARIMA(df['y'], order=(p, d, q))
                            fitted = model.fit()
                            if fitted.aic < best_aic:
                                best_aic = fitted.aic
                                best_order = (p, d, q)
                        except:
                            continue
        
        return best_order
    
    def fit(self, df, order=None, auto_order=True):
        """
        Fit ARIMA model to the data.
        
        Args:
            df (pandas.DataFrame): Data with 'ds' and 'y' columns
            order (tuple): ARIMA order (p, d, q)
            auto_order (bool): Automatically find best order
        """
        if not ARIMA_AVAILABLE:
            raise ImportError("ARIMA is not available")
        
        try:
            if auto_order or order is None:
                self.order = self.find_best_order(df)
            else:
                self.order = order
            
            with st.spinner(f"Training ARIMA{self.order} model..."):
                self.model = ARIMA(df['y'], order=self.order)
                self.fitted_model = self.model.fit()
            
            self.is_fitted = True
            return True
            
        except Exception as e:
            st.error(f"Error fitting ARIMA model: {str(e)}")
            return False
    
    def predict(self, periods=30):
        """
        Generate forecasts.
        
        Args:
            periods (int): Number of periods to forecast
            
        Returns:
            pandas.DataFrame: Forecast results
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        
        try:
            forecast = self.fitted_model.forecast(steps=periods)
            conf_int = self.fitted_model.get_forecast(steps=periods).conf_int()
            
            # Create future dates
            last_date = pd.to_datetime(self.fitted_model.data.dates[-1])
            future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), 
                                       periods=periods, freq='D')
            
            # Create forecast dataframe
            forecast_df = pd.DataFrame({
                'ds': future_dates,
                'yhat': forecast,
                'yhat_lower': conf_int.iloc[:, 0],
                'yhat_upper': conf_int.iloc[:, 1],
                'model': 'ARIMA'
            })
            
            return forecast_df
            
        except Exception as e:
            st.error(f"Error generating ARIMA forecast: {str(e)}")
            return None


def create_forecast_plot(historical_data, forecast_data, title="Demand Forecast"):
    """
    Create an interactive forecast plot.
    
    Args:
        historical_data (pandas.DataFrame): Historical data with 'ds' and 'y' columns
        forecast_data (pandas.DataFrame): Forecast data
        title (str): Plot title
        
    Returns:
        plotly.graph_objects.Figure: Interactive plot
    """
    fig = go.Figure()
    
    # Historical data
    fig.add_trace(go.Scatter(
        x=historical_data['ds'],
        y=historical_data['y'],
        mode='lines',
        name='Historical Data',
        line=dict(color='blue', width=2)
    ))
    
    # Forecast
    fig.add_trace(go.Scatter(
        x=forecast_data['ds'],
        y=forecast_data['yhat'],
        mode='lines',
        name='Forecast',
        line=dict(color='red', width=2, dash='dash')
    ))
    
    # Confidence intervals
    if 'yhat_upper' in forecast_data.columns and 'yhat_lower' in forecast_data.columns:
        fig.add_trace(go.Scatter(
            x=forecast_data['ds'],
            y=forecast_data['yhat_upper'],
            mode='lines',
            line=dict(width=0),
            showlegend=False,
            hoverinfo='skip'
        ))
        
        fig.add_trace(go.Scatter(
            x=forecast_data['ds'],
            y=forecast_data['yhat_lower'],
            mode='lines',
            line=dict(width=0),
            fill='tonexty',
            fillcolor='rgba(255, 0, 0, 0.2)',
            name='Confidence Interval',
            hoverinfo='skip'
        ))
    
    fig.update_layout(
        title=title,
        xaxis_title='Date',
        yaxis_title='Demand',
        hovermode='x unified',
        template='plotly_white',
        height=500
    )
    
    return fig


def evaluate_forecast_accuracy(actual, predicted):
    """
    Calculate forecast accuracy metrics.
    
    Args:
        actual (array-like): Actual values
        predicted (array-like): Predicted values
        
    Returns:
        dict: Accuracy metrics
    """
    actual = np.array(actual)
    predicted = np.array(predicted)
    
    # Remove any NaN values
    mask = ~(np.isnan(actual) | np.isnan(predicted))
    actual = actual[mask]
    predicted = predicted[mask]
    
    if len(actual) == 0:
        return {}
    
    metrics = {}
    
    # Mean Absolute Error
    metrics['MAE'] = np.mean(np.abs(actual - predicted))
    
    # Mean Squared Error
    metrics['MSE'] = np.mean((actual - predicted) ** 2)
    
    # Root Mean Squared Error
    metrics['RMSE'] = np.sqrt(metrics['MSE'])
    
    # Mean Absolute Percentage Error
    non_zero_mask = actual != 0
    if np.any(non_zero_mask):
        metrics['MAPE'] = np.mean(np.abs((actual[non_zero_mask] - predicted[non_zero_mask]) / actual[non_zero_mask])) * 100
    else:
        metrics['MAPE'] = np.inf
    
    return metrics


def run_forecast(df, model_type='Prophet', forecast_periods=30, **kwargs):
    """
    Run forecasting with the specified model.
    
    Args:
        df (pandas.DataFrame): Data with 'ds' and 'y' columns
        model_type (str): 'Prophet' or 'ARIMA'
        forecast_periods (int): Number of periods to forecast
        **kwargs: Additional model parameters
        
    Returns:
        tuple: (forecast_dataframe, model_object, success_flag)
    """
    try:
        if model_type == 'Prophet' and PROPHET_AVAILABLE:
            forecaster = ProphetForecaster()
            success = forecaster.fit(df, **kwargs)
            if success:
                forecast = forecaster.predict(periods=forecast_periods)
                return forecast, forecaster, True
        
        elif model_type == 'ARIMA' and ARIMA_AVAILABLE:
            forecaster = ARIMAForecaster()
            success = forecaster.fit(df, **kwargs)
            if success:
                forecast = forecaster.predict(periods=forecast_periods)
                return forecast, forecaster, True
        
        else:
            st.error(f"Model {model_type} is not available")
            return None, None, False
    
    except Exception as e:
        st.error(f"Error running forecast: {str(e)}")
        return None, None, False
    
    return None, None, False
