"""
Inventory Planning Page for the Inventory Prediction System.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
from utils.inventory_logic import (
    calculate_inventory_recommendations, create_inventory_dashboard_plot,
    create_stock_status_chart, generate_inventory_alerts, optimize_inventory_parameters
)


def show_code_snippet(code, description):
    """Show code snippet if code view is enabled."""
    if st.session_state.show_code:
        with st.expander(f"💻 Code: {description}"):
            st.code(code, language='python')


def show_inventory_page():
    """Display the inventory planning page."""
    st.title("📦 Inventory Planning")
    st.markdown("Generate smart inventory recommendations based on demand forecasts.")
    
    if st.session_state.forecast_data is None:
        st.warning("⚠️ Please generate a forecast first in the 'Forecasting' section.")
        return
    
    forecast_data = st.session_state.forecast_data
    
    # Inventory configuration
    st.subheader("⚙️ Inventory Configuration")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        buffer_percentage = st.slider(
            "Safety Stock Buffer (%)",
            min_value=5,
            max_value=100,
            value=20,
            step=5,
            help="Additional stock percentage to maintain as safety buffer"
        )
    
    with col2:
        current_stock = st.number_input(
            "Current Stock Level",
            min_value=0.0,
            value=100.0,
            step=10.0,
            help="Current inventory level in units"
        )
    
    with col3:
        lead_time_days = st.number_input(
            "Lead Time (Days)",
            min_value=1,
            max_value=30,
            value=7,
            help="Time required to receive new inventory"
        )
    
    # Advanced parameters
    with st.expander("🔧 Advanced Parameters"):
        col1, col2 = st.columns(2)
        
        with col1:
            min_order_quantity = st.number_input(
                "Minimum Order Quantity",
                min_value=1.0,
                value=50.0,
                step=10.0,
                help="Minimum quantity that must be ordered"
            )
            
            cost_per_unit = st.number_input(
                "Cost per Unit ($)",
                min_value=0.01,
                value=10.0,
                step=0.50,
                help="Cost per unit for inventory calculations"
            )
        
        with col2:
            max_stock_level = st.number_input(
                "Maximum Stock Level (Optional)",
                min_value=0.0,
                value=0.0,
                step=50.0,
                help="Maximum inventory level constraint (0 = no limit)"
            )
            
            holding_cost_rate = st.number_input(
                "Holding Cost Rate (%/year)",
                min_value=0.1,
                max_value=50.0,
                value=10.0,
                step=0.5,
                help="Annual holding cost as percentage of unit cost"
            ) / 100
    
    # Set max_stock_level to None if 0
    if max_stock_level == 0:
        max_stock_level = None
    
    # Generate inventory recommendations
    if st.button("📦 Generate Inventory Plan", type="primary"):
        with st.spinner("Calculating inventory recommendations..."):
            inventory_data = calculate_inventory_recommendations(
                forecast_data,
                buffer_percentage=buffer_percentage,
                current_stock=current_stock,
                lead_time_days=lead_time_days,
                min_order_quantity=min_order_quantity,
                max_stock_level=max_stock_level
            )
        
        show_code_snippet(
            f"""
# Calculate inventory recommendations
from utils.inventory_logic import calculate_inventory_recommendations

inventory_data = calculate_inventory_recommendations(
    forecast_data,
    buffer_percentage={buffer_percentage},
    current_stock={current_stock},
    lead_time_days={lead_time_days},
    min_order_quantity={min_order_quantity},
    max_stock_level={max_stock_level}
)
            """,
            "Inventory Recommendations"
        )
        
        if inventory_data is not None:
            st.session_state.inventory_data = inventory_data
            
            st.success("✅ Inventory recommendations generated successfully!")
            
            # Key metrics
            st.subheader("📊 Key Metrics")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                total_orders = inventory_data['order_quantity'].sum()
                st.metric("Total Orders Needed", f"{total_orders:.0f}")
            
            with col2:
                reorder_days = len(inventory_data[inventory_data['order_quantity'] > 0])
                st.metric("Reorder Days", reorder_days)
            
            with col3:
                avg_stock = inventory_data['current_stock_level'].mean()
                st.metric("Avg Stock Level", f"{avg_stock:.1f}")
            
            with col4:
                total_cost = total_orders * cost_per_unit
                st.metric("Total Order Cost", f"${total_cost:,.0f}")
            
            # Inventory dashboard
            st.subheader("📈 Inventory Dashboard")
            
            fig_dashboard = create_inventory_dashboard_plot(inventory_data)
            st.plotly_chart(fig_dashboard, use_container_width=True)
            
            # Stock status analysis
            col1, col2 = st.columns(2)
            
            with col1:
                fig_status = create_stock_status_chart(inventory_data)
                st.plotly_chart(fig_status, use_container_width=True)
            
            with col2:
                # Stock level distribution
                fig_dist = px.histogram(
                    inventory_data,
                    x='current_stock_level',
                    nbins=20,
                    title="Stock Level Distribution",
                    labels={'current_stock_level': 'Stock Level', 'count': 'Days'}
                )
                st.plotly_chart(fig_dist, use_container_width=True)
            
            # Inventory alerts
            st.subheader("🚨 Inventory Alerts")
            
            alerts = generate_inventory_alerts(inventory_data, alert_threshold_days=14)
            
            if alerts:
                for alert in alerts:
                    if alert['type'] == 'critical':
                        st.error(alert['message'])
                    elif alert['type'] == 'reorder':
                        st.warning(alert['message'])
                    elif alert['type'] == 'overstock':
                        st.info(alert['message'])
                    elif alert['type'] == 'high_demand':
                        st.info(alert['message'])
            else:
                st.success("✅ No critical inventory alerts detected!")
            
            # Recommended inventory plan table
            st.subheader("📋 Recommended Inventory Plan")
            
            # Format data for display
            display_data = inventory_data.copy()
            display_data['ds'] = pd.to_datetime(display_data['ds']).dt.strftime('%Y-%m-%d')
            
            # Select key columns for display
            key_columns = [
                'ds', 'predicted_demand', 'recommended_stock', 
                'current_stock_level', 'order_quantity', 'stock_status'
            ]
            
            available_columns = [col for col in key_columns if col in display_data.columns]
            display_data = display_data[available_columns]
            
            # Color-code rows based on stock status
            def highlight_status(row):
                if row['stock_status'] == 'Critical':
                    return ['background-color: #ffebee'] * len(row)
                elif row['stock_status'] == 'Low':
                    return ['background-color: #fff3e0'] * len(row)
                elif row['stock_status'] == 'Overstock':
                    return ['background-color: #f3e5f5'] * len(row)
                else:
                    return [''] * len(row)
            
            # Show first 30 rows with styling
            styled_data = display_data.head(30).style.apply(highlight_status, axis=1)
            st.dataframe(styled_data, use_container_width=True)
            
            if len(display_data) > 30:
                st.info(f"Showing first 30 rows of {len(display_data)} recommendations. Full data available in reports.")
            
            # Optimization suggestions
            st.subheader("🎯 Optimization Suggestions")
            
            if st.session_state.cleaned_data is not None:
                optimization_params = optimize_inventory_parameters(
                    st.session_state.cleaned_data,
                    forecast_data,
                    cost_per_unit=cost_per_unit,
                    holding_cost_rate=holding_cost_rate
                )
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.markdown("**Optimized Parameters:**")
                    st.write(f"• Economic Order Quantity: {optimization_params['eoq']:.0f} units")
                    st.write(f"• Safety Stock: {optimization_params['safety_stock']:.0f} units")
                    st.write(f"• Reorder Point: {optimization_params['reorder_point']:.0f} units")
                
                with col2:
                    st.markdown("**Recommendations:**")
                    st.write(f"• Optimal Buffer: {optimization_params['optimal_buffer_percentage']:.1f}%")
                    st.write(f"• Service Level: {optimization_params['recommended_service_level']:.0f}%")
                    
                    if optimization_params['optimal_buffer_percentage'] != buffer_percentage:
                        diff = optimization_params['optimal_buffer_percentage'] - buffer_percentage
                        if diff > 0:
                            st.info(f"💡 Consider increasing buffer to {optimization_params['optimal_buffer_percentage']:.1f}% (+{diff:.1f}%)")
                        else:
                            st.info(f"💡 Consider decreasing buffer to {optimization_params['optimal_buffer_percentage']:.1f}% ({diff:.1f}%)")
            
            # Cost analysis
            st.subheader("💰 Cost Analysis")
            
            # Calculate various costs
            total_holding_cost = (inventory_data['current_stock_level'] * cost_per_unit * holding_cost_rate / 365).sum()
            total_order_cost = total_orders * cost_per_unit
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Total Order Cost", f"${total_order_cost:,.0f}")
            
            with col2:
                st.metric("Estimated Holding Cost", f"${total_holding_cost:,.0f}")
            
            with col3:
                total_cost = total_order_cost + total_holding_cost
                st.metric("Total Inventory Cost", f"${total_cost:,.0f}")
            
            # Cost breakdown chart
            cost_data = pd.DataFrame({
                'Cost Type': ['Order Cost', 'Holding Cost'],
                'Amount': [total_order_cost, total_holding_cost]
            })
            
            fig_cost = px.pie(
                cost_data,
                values='Amount',
                names='Cost Type',
                title="Inventory Cost Breakdown"
            )
            st.plotly_chart(fig_cost, use_container_width=True)
            
            # Action items
            st.subheader("✅ Action Items")
            
            action_items = generate_action_items(inventory_data, alerts)
            
            for i, item in enumerate(action_items, 1):
                st.write(f"{i}. {item}")
            
            # Next steps
            st.subheader("🎯 Next Steps")
            st.info(
                "Your inventory plan is ready! You can now:\n"
                "- Export detailed reports in the **Reports & Export** section\n"
                "- Adjust parameters and regenerate if needed\n"
                "- Implement the recommended reorder schedule"
            )
        
        else:
            st.error("❌ Failed to generate inventory recommendations. Please check your forecast data.")


def generate_action_items(inventory_data, alerts):
    """Generate actionable items based on inventory analysis."""
    action_items = []
    
    # Immediate actions based on alerts
    critical_days = len(inventory_data[inventory_data['stock_status'] == 'Critical'])
    if critical_days > 0:
        action_items.append(f"🚨 **URGENT**: Address {critical_days} critical stock situations immediately")
    
    # Upcoming reorders
    next_7_days = inventory_data[
        (inventory_data['order_quantity'] > 0) & 
        (pd.to_datetime(inventory_data['ds']) <= datetime.now() + timedelta(days=7))
    ]
    
    if not next_7_days.empty:
        total_qty = next_7_days['order_quantity'].sum()
        action_items.append(f"📦 Place orders for {total_qty:.0f} units within the next 7 days")
    
    # High demand periods
    high_demand_threshold = inventory_data['predicted_demand'].quantile(0.9)
    high_demand_days = inventory_data[inventory_data['predicted_demand'] > high_demand_threshold]
    
    if not high_demand_days.empty:
        peak_date = high_demand_days.loc[high_demand_days['predicted_demand'].idxmax(), 'ds']
        action_items.append(f"📈 Prepare for peak demand period around {pd.to_datetime(peak_date).strftime('%Y-%m-%d')}")
    
    # Overstock situations
    overstock_days = len(inventory_data[inventory_data['stock_status'] == 'Overstock'])
    if overstock_days > 0:
        action_items.append(f"📉 Review overstock situations on {overstock_days} days - consider promotions or reduced ordering")
    
    # Regular monitoring
    action_items.append("📊 Monitor actual demand vs forecast and adjust parameters as needed")
    action_items.append("🔄 Update forecasts regularly with new sales data")
    
    return action_items
