"""
Universal Data Analysis & Prediction System
An adaptable platform for analyzing any type of data with forecasting capabilities
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Universal Data Analysis System",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Enhanced Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.8rem;
        font-weight: bold;
        background: linear-gradient(90deg, #1f77b4, #ff7f0e);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-align: center;
        margin-bottom: 2rem;
        padding: 1rem 0;
    }
    .metric-card {
        background: linear-gradient(135deg, #f0f2f6, #e8f4fd);
        padding: 1.5rem;
        border-radius: 1rem;
        border-left: 5px solid #1f77b4;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 0.5rem 0;
    }
    .sidebar .sidebar-content {
        background: linear-gradient(180deg, #f8f9fa, #e9ecef);
    }
    .nav-item {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    .nav-item:hover {
        background-color: #e3f2fd;
        border-left-color: #1f77b4;
        transform: translateX(5px);
    }
    .nav-item.active {
        background-color: #1f77b4;
        color: white;
        border-left-color: #ff7f0e;
    }
    .data-upload-area {
        border: 2px dashed #1f77b4;
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
        margin: 1rem 0;
    }
    .insight-box {
        background: linear-gradient(135deg, #fff3e0, #ffecb3);
        border-left: 5px solid #ff9800;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .success-box {
        background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
        border-left: 5px solid #4caf50;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .warning-box {
        background: linear-gradient(135deg, #fff3e0, #ffe0b2);
        border-left: 5px solid #ff9800;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False
if 'uploaded_data' not in st.session_state:
    st.session_state.uploaded_data = None
if 'column_mapping' not in st.session_state:
    st.session_state.column_mapping = {}
if 'analysis_type' not in st.session_state:
    st.session_state.analysis_type = 'General Analysis'
if 'current_page' not in st.session_state:
    st.session_state.current_page = 'Home'

def load_uploaded_data(uploaded_file):
    """Load data from uploaded file"""
    try:
        if uploaded_file.name.endswith('.csv'):
            data = pd.read_csv(uploaded_file)
        elif uploaded_file.name.endswith(('.xlsx', '.xls')):
            data = pd.read_excel(uploaded_file)
        else:
            st.error("❌ Unsupported file format. Please upload CSV or Excel files.")
            return None

        return data
    except Exception as e:
        st.error(f"❌ Error loading file: {str(e)}")
        return None

def create_sample_data(data_type="sales"):
    """Create sample data based on type"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)

    if data_type == "sales":
        # Sales data
        trend = np.linspace(100, 150, len(dates))
        seasonal = 20 * np.sin(2 * np.pi * np.arange(len(dates)) / 365.25)
        weekly = 10 * np.sin(2 * np.pi * np.arange(len(dates)) / 7)
        noise = np.random.normal(0, 15, len(dates))
        values = trend + seasonal + weekly + noise
        values = np.maximum(values, 10)

        return pd.DataFrame({
            'Date': dates,
            'Product': 'Product A',
            'Sales': values.round(0).astype(int),
            'Store': 'Store 1',
            'Price': 10.99
        })

    elif data_type == "financial":
        # Financial data
        base_price = 100
        returns = np.random.normal(0.001, 0.02, len(dates))
        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        return pd.DataFrame({
            'Date': dates,
            'Stock_Symbol': 'SAMPLE',
            'Price': prices,
            'Volume': np.random.randint(1000, 10000, len(dates)),
            'Market_Cap': np.array(prices) * np.random.randint(1000000, 2000000, len(dates))
        })

    elif data_type == "website":
        # Website analytics data
        base_visitors = 1000
        trend = np.linspace(0.8, 1.2, len(dates))
        seasonal = 0.2 * np.sin(2 * np.pi * np.arange(len(dates)) / 7)  # Weekly pattern
        noise = np.random.normal(0, 0.1, len(dates))
        multiplier = trend + seasonal + noise
        visitors = (base_visitors * multiplier).astype(int)

        return pd.DataFrame({
            'Date': dates,
            'Visitors': visitors,
            'Page_Views': visitors * np.random.uniform(2, 5, len(dates)),
            'Bounce_Rate': np.random.uniform(0.3, 0.7, len(dates)),
            'Conversion_Rate': np.random.uniform(0.02, 0.08, len(dates))
        })

    else:
        # Generic numerical data
        return pd.DataFrame({
            'Date': dates,
            'Value_1': np.random.normal(100, 20, len(dates)),
            'Value_2': np.random.normal(50, 10, len(dates)),
            'Category': np.random.choice(['A', 'B', 'C'], len(dates)),
            'Score': np.random.uniform(0, 100, len(dates))
        })

def detect_column_types(data):
    """Automatically detect column types for analysis"""
    column_info = {}

    for col in data.columns:
        col_data = data[col]

        # Check if it's a date column
        if col_data.dtype == 'object':
            try:
                pd.to_datetime(col_data.head(10))
                column_info[col] = 'date'
                continue
            except:
                pass

        # Check if it's numerical
        if pd.api.types.is_numeric_dtype(col_data):
            # Check if it could be a target variable (has reasonable variance)
            if col_data.std() > 0 and col_data.nunique() > 10:
                column_info[col] = 'numerical_target'
            else:
                column_info[col] = 'numerical'
        else:
            # Categorical
            if col_data.nunique() < len(col_data) * 0.5:  # Less than 50% unique values
                column_info[col] = 'categorical'
            else:
                column_info[col] = 'text'

    return column_info

def universal_forecast(data, date_col, target_col, periods=30):
    """Universal forecasting function for any numerical data"""
    try:
        # Prepare data
        forecast_data = data[[date_col, target_col]].copy()
        forecast_data[date_col] = pd.to_datetime(forecast_data[date_col])
        forecast_data = forecast_data.sort_values(date_col)

        # Simple moving average with trend
        recent_values = forecast_data[target_col].tail(min(30, len(forecast_data))).mean()
        if len(forecast_data) > 7:
            trend = (forecast_data[target_col].tail(7).mean() - forecast_data[target_col].head(7).mean()) / len(forecast_data)
        else:
            trend = 0

        # Generate future dates
        last_date = forecast_data[date_col].max()
        freq = pd.infer_freq(forecast_data[date_col])
        if freq is None:
            freq = 'D'  # Default to daily

        future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=periods, freq=freq)
        forecast_values = []

        for i in range(periods):
            forecast = recent_values + (trend * i)
            # Add uncertainty based on historical volatility
            uncertainty = forecast_data[target_col].std() * 0.1
            forecast_values.append({
                'Date': future_dates[i],
                'Forecast': forecast,
                'Lower': forecast - uncertainty,
                'Upper': forecast + uncertainty
            })

        return pd.DataFrame(forecast_values)

    except Exception as e:
        st.error(f"Error in forecasting: {str(e)}")
        return None

def calculate_inventory_plan(forecast_df, buffer_pct=20, current_stock=100):
    """Calculate inventory recommendations"""
    inventory_plan = forecast_df.copy()
    inventory_plan['Buffer_Stock'] = inventory_plan['Forecast'] * (buffer_pct / 100)
    inventory_plan['Recommended_Stock'] = inventory_plan['Forecast'] + inventory_plan['Buffer_Stock']
    
    # Simple reorder logic
    inventory_plan['Current_Stock'] = current_stock
    inventory_plan['Order_Quantity'] = 0
    inventory_plan['Status'] = 'Normal'
    
    for i in range(len(inventory_plan)):
        if i > 0:
            prev_stock = inventory_plan.iloc[i-1]['Current_Stock']
            demand = inventory_plan.iloc[i-1]['Forecast']
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Current_Stock')] = max(0, prev_stock - demand)
        
        current = inventory_plan.iloc[i]['Current_Stock']
        recommended = inventory_plan.iloc[i]['Recommended_Stock']
        
        if current < recommended * 0.5:
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Order_Quantity')] = recommended - current
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Status')] = 'Reorder Needed'
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Current_Stock')] = recommended
    
    return inventory_plan

# Enhanced Sidebar Navigation
st.sidebar.markdown("""
<div style='text-align: center; padding: 1rem 0;'>
    <h1 style='color: #1f77b4; margin: 0;'>📊 Universal Data Analysis</h1>
    <p style='color: #666; margin: 0.5rem 0;'>Analyze Any Data, Anywhere</p>
</div>
""", unsafe_allow_html=True)

st.sidebar.markdown("---")

# Navigation with radio buttons (list style)
pages = [
    "🏠 Home",
    "📁 Data Upload",
    "📊 Data Analysis",
    "🔮 Forecasting",
    "📈 Advanced Analytics",
    "📋 Reports"
]

# Custom navigation
st.sidebar.markdown("### 🧭 Navigation")
page = st.sidebar.radio("", pages, key="navigation")

# Add data info sidebar
if st.session_state.data_loaded and st.session_state.uploaded_data is not None:
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📊 Data Info")
    data = st.session_state.uploaded_data
    st.sidebar.info(f"**Rows:** {len(data)}")
    st.sidebar.info(f"**Columns:** {len(data.columns)}")

    # Show column types
    column_types = detect_column_types(data)
    date_cols = [col for col, type_ in column_types.items() if type_ == 'date']
    numerical_cols = [col for col, type_ in column_types.items() if type_ in ['numerical', 'numerical_target']]

    if date_cols:
        st.sidebar.success(f"📅 Date columns: {len(date_cols)}")
    if numerical_cols:
        st.sidebar.success(f"🔢 Numerical columns: {len(numerical_cols)}")

# Main content
if page == "🏠 Home":
    st.markdown('<h1 class="main-header">📊 Universal Data Analysis System</h1>', unsafe_allow_html=True)

    st.markdown("""
    <div class="success-box">
    Welcome to the <strong>Universal Data Analysis System</strong> - your comprehensive platform for analyzing
    any type of data with advanced visualization and forecasting capabilities.
    </div>
    """, unsafe_allow_html=True)

    # Feature cards
    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("""
        <div class="metric-card">
            <h3>📁 Universal Data Upload</h3>
            <p>Upload any CSV or Excel file and let our system automatically detect column types and suggest analysis approaches.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown("""
        <div class="metric-card">
            <h3>📊 Smart Analysis</h3>
            <p>Automatic data profiling, visualization, and insights generation for any type of dataset.</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown("""
        <div class="metric-card">
            <h3>🔮 Predictive Analytics</h3>
            <p>Advanced forecasting and trend analysis for time-series data across any domain.</p>
        </div>
        """, unsafe_allow_html=True)

    st.markdown("## 🎯 Supported Data Types")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        **📈 Business & Sales Data**
        - Sales transactions and revenue
        - Customer behavior and metrics
        - Inventory and supply chain data

        **💰 Financial Data**
        - Stock prices and trading volumes
        - Financial statements and ratios
        - Economic indicators
        """)

    with col2:
        st.markdown("""
        **🌐 Web Analytics**
        - Website traffic and user behavior
        - Social media metrics
        - Marketing campaign performance

        **🔬 Research & Scientific Data**
        - Experimental results
        - Survey responses
        - Any time-series measurements
        """)

    st.markdown("## 🚀 Quick Start Options")

    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("📈 Try Sales Data", type="primary", use_container_width=True):
            st.session_state.uploaded_data = create_sample_data("sales")
            st.session_state.data_loaded = True
            st.session_state.analysis_type = "Sales Analysis"
            st.success("✅ Sample sales data loaded!")
            st.rerun()

    with col2:
        if st.button("💰 Try Financial Data", type="primary", use_container_width=True):
            st.session_state.uploaded_data = create_sample_data("financial")
            st.session_state.data_loaded = True
            st.session_state.analysis_type = "Financial Analysis"
            st.success("✅ Sample financial data loaded!")
            st.rerun()

    with col3:
        if st.button("🌐 Try Web Analytics", type="primary", use_container_width=True):
            st.session_state.uploaded_data = create_sample_data("website")
            st.session_state.data_loaded = True
            st.session_state.analysis_type = "Web Analytics"
            st.success("✅ Sample web data loaded!")
            st.rerun()

    if st.session_state.data_loaded:
        st.markdown("""
        <div class="insight-box">
        <strong>🎉 Data loaded successfully!</strong> Navigate to other sections to explore your data:
        <br>• <strong>Data Analysis</strong> - Explore patterns and insights
        <br>• <strong>Forecasting</strong> - Generate predictions
        <br>• <strong>Advanced Analytics</strong> - Deep dive analysis
        </div>
        """, unsafe_allow_html=True)

elif page == "📁 Data Upload":
    st.title("📁 Data Upload & Configuration")

    st.markdown("""
    <div class="data-upload-area">
        <h3>📤 Upload Your Data</h3>
        <p>Upload any CSV or Excel file to get started with analysis</p>
    </div>
    """, unsafe_allow_html=True)

    # File upload
    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['csv', 'xlsx', 'xls'],
        help="Upload CSV or Excel files up to 200MB"
    )

    if uploaded_file is not None:
        with st.spinner("Loading your data..."):
            data = load_uploaded_data(uploaded_file)

        if data is not None:
            st.session_state.uploaded_data = data
            st.session_state.data_loaded = True

            st.success(f"✅ File loaded successfully! {len(data)} rows, {len(data.columns)} columns")

            # Data preview
            st.subheader("👀 Data Preview")
            st.dataframe(data.head(10), use_container_width=True)

            # Column analysis
            st.subheader("🔍 Column Analysis")
            column_types = detect_column_types(data)

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**📊 Column Types Detected:**")
                type_counts = {}
                for col, col_type in column_types.items():
                    type_counts[col_type] = type_counts.get(col_type, 0) + 1

                for col_type, count in type_counts.items():
                    icon = {"date": "📅", "numerical": "🔢", "numerical_target": "🎯", "categorical": "🏷️", "text": "📝"}.get(col_type, "❓")
                    st.write(f"{icon} {col_type.replace('_', ' ').title()}: {count} columns")

            with col2:
                st.markdown("**🎯 Suggested Analysis Type:**")

                date_cols = [col for col, type_ in column_types.items() if type_ == 'date']
                numerical_cols = [col for col, type_ in column_types.items() if type_ in ['numerical', 'numerical_target']]

                if date_cols and numerical_cols:
                    st.success("📈 Time Series Analysis Recommended")
                    st.session_state.analysis_type = "Time Series Analysis"
                elif numerical_cols:
                    st.info("📊 Statistical Analysis Recommended")
                    st.session_state.analysis_type = "Statistical Analysis"
                else:
                    st.info("🔍 Exploratory Data Analysis Recommended")
                    st.session_state.analysis_type = "Exploratory Analysis"

            # Column mapping for analysis
            if date_cols and numerical_cols:
                st.subheader("🔗 Configure Analysis")

                col1, col2 = st.columns(2)

                with col1:
                    date_column = st.selectbox("📅 Select Date Column", date_cols)
                    st.session_state.column_mapping['date'] = date_column

                with col2:
                    target_column = st.selectbox("🎯 Select Target Column", numerical_cols)
                    st.session_state.column_mapping['target'] = target_column

                if st.button("✅ Configure Analysis", type="primary"):
                    st.success("🎉 Analysis configured! Navigate to other sections to explore your data.")
                    st.rerun()

elif page == "📊 Data Analysis":
    st.title("📊 Universal Data Analysis")

    if not st.session_state.data_loaded:
        st.markdown("""
        <div class="warning-box">
        ⚠️ <strong>No data loaded.</strong> Please upload your data or load sample data from the Home page first.
        </div>
        """, unsafe_allow_html=True)

        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📈 Load Sales Sample"):
                st.session_state.uploaded_data = create_sample_data("sales")
                st.session_state.data_loaded = True
                st.rerun()
        with col2:
            if st.button("💰 Load Financial Sample"):
                st.session_state.uploaded_data = create_sample_data("financial")
                st.session_state.data_loaded = True
                st.rerun()
        with col3:
            if st.button("🌐 Load Web Sample"):
                st.session_state.uploaded_data = create_sample_data("website")
                st.session_state.data_loaded = True
                st.rerun()
    else:
        data = st.session_state.uploaded_data
        column_types = detect_column_types(data)
        
        # Universal data overview
        st.subheader("📋 Data Overview")

        # Get date and numerical columns
        date_cols = [col for col, type_ in column_types.items() if type_ == 'date']
        numerical_cols = [col for col, type_ in column_types.items() if type_ in ['numerical', 'numerical_target']]
        categorical_cols = [col for col, type_ in column_types.items() if type_ == 'categorical']

        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Records", len(data))
        with col2:
            st.metric("Columns", len(data.columns))
        with col3:
            if date_cols:
                date_range = (pd.to_datetime(data[date_cols[0]]).max() - pd.to_datetime(data[date_cols[0]]).min()).days
                st.metric("Date Range (Days)", date_range)
            else:
                st.metric("Numerical Columns", len(numerical_cols))
        with col4:
            if numerical_cols:
                avg_value = data[numerical_cols[0]].mean()
                st.metric(f"Avg {numerical_cols[0]}", f"{avg_value:.1f}")
            else:
                st.metric("Categorical Columns", len(categorical_cols))

        # Data preview
        st.subheader("👀 Data Preview")
        st.dataframe(data.head(10), use_container_width=True)

        # Universal visualizations
        if date_cols and numerical_cols:
            # Time series analysis
            st.subheader("📈 Time Series Analysis")

            date_col = date_cols[0]
            target_col = st.selectbox("Select column to analyze:", numerical_cols)

            # Convert date column
            data_viz = data.copy()
            data_viz[date_col] = pd.to_datetime(data_viz[date_col])

            fig = px.line(data_viz, x=date_col, y=target_col,
                         title=f'{target_col} Over Time')
            fig.update_layout(height=500)
            st.plotly_chart(fig, use_container_width=True)

            # Seasonal analysis if enough data
            if len(data_viz) > 30:
                st.subheader("📅 Seasonal Patterns")

                col1, col2 = st.columns(2)

                with col1:
                    # Monthly pattern
                    data_viz['Month'] = data_viz[date_col].dt.month_name()
                    monthly_avg = data_viz.groupby('Month')[target_col].mean().reindex([
                        'January', 'February', 'March', 'April', 'May', 'June',
                        'July', 'August', 'September', 'October', 'November', 'December'
                    ])

                    fig_monthly = px.bar(x=monthly_avg.index, y=monthly_avg.values,
                                       title=f'Average {target_col} by Month')
                    st.plotly_chart(fig_monthly, use_container_width=True)

                with col2:
                    # Day of week pattern
                    data_viz['DayOfWeek'] = data_viz[date_col].dt.day_name()
                    dow_avg = data_viz.groupby('DayOfWeek')[target_col].mean().reindex([
                        'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
                    ])

                    fig_dow = px.bar(x=dow_avg.index, y=dow_avg.values,
                                   title=f'Average {target_col} by Day of Week')
                    st.plotly_chart(fig_dow, use_container_width=True)

        elif numerical_cols:
            # Statistical analysis for non-time series data
            st.subheader("📊 Statistical Analysis")

            selected_cols = st.multiselect("Select columns to analyze:", numerical_cols, default=numerical_cols[:3])

            if selected_cols:
                # Correlation matrix
                if len(selected_cols) > 1:
                    st.subheader("🔗 Correlation Analysis")
                    corr_matrix = data[selected_cols].corr()
                    fig_corr = px.imshow(corr_matrix, text_auto=True, aspect="auto",
                                       title="Correlation Matrix")
                    st.plotly_chart(fig_corr, use_container_width=True)

                # Distribution analysis
                st.subheader("📈 Distribution Analysis")

                for col in selected_cols[:2]:  # Show first 2 to avoid clutter
                    fig_hist = px.histogram(data, x=col, title=f'Distribution of {col}')
                    st.plotly_chart(fig_hist, use_container_width=True)

        # Categorical analysis
        if categorical_cols:
            st.subheader("🏷️ Categorical Analysis")

            cat_col = st.selectbox("Select categorical column:", categorical_cols)

            # Value counts
            value_counts = data[cat_col].value_counts().head(10)
            fig_cat = px.bar(x=value_counts.index, y=value_counts.values,
                           title=f'Top 10 Values in {cat_col}')
            st.plotly_chart(fig_cat, use_container_width=True)

        # Data quality insights
        st.subheader("🔍 Data Quality Insights")

        col1, col2 = st.columns(2)

        with col1:
            # Missing values
            missing_data = data.isnull().sum()
            missing_data = missing_data[missing_data > 0]

            if len(missing_data) > 0:
                fig_missing = px.bar(x=missing_data.index, y=missing_data.values,
                                   title="Missing Values by Column")
                st.plotly_chart(fig_missing, use_container_width=True)
            else:
                st.success("✅ No missing values found!")

        with col2:
            # Data statistics
            st.markdown("**📊 Data Statistics**")
            if numerical_cols:
                stats_data = data[numerical_cols].describe()
                st.dataframe(stats_data, use_container_width=True)
            else:
                st.info("No numerical columns for statistics")

elif page == "🔮 Forecasting":
    st.title("🔮 Universal Forecasting")

    if not st.session_state.data_loaded:
        st.markdown("""
        <div class="warning-box">
        ⚠️ <strong>No data loaded.</strong> Please upload your data or load sample data first.
        </div>
        """, unsafe_allow_html=True)
    else:
        data = st.session_state.uploaded_data
        column_types = detect_column_types(data)

        # Get date and numerical columns
        date_cols = [col for col, type_ in column_types.items() if type_ == 'date']
        numerical_cols = [col for col, type_ in column_types.items() if type_ in ['numerical', 'numerical_target']]

        if not date_cols or not numerical_cols:
            st.error("❌ Forecasting requires both date and numerical columns in your data.")
            st.info("💡 Your data should have at least one date column and one numerical column for forecasting.")
        else:
            # Universal forecast configuration
            st.subheader("⚙️ Forecast Configuration")

            col1, col2, col3 = st.columns(3)

            with col1:
                date_col = st.selectbox("📅 Select Date Column", date_cols)
            with col2:
                target_col = st.selectbox("🎯 Select Target Column", numerical_cols)
            with col3:
                forecast_periods = st.selectbox("Forecast Periods", [30, 60, 90, 120], index=0)

            # Show data preview for selected columns
            st.subheader("📊 Selected Data Preview")
            preview_data = data[[date_col, target_col]].copy()
            preview_data[date_col] = pd.to_datetime(preview_data[date_col])
            preview_data = preview_data.sort_values(date_col).tail(10)
            st.dataframe(preview_data, use_container_width=True)

            if st.button("🔮 Generate Forecast", type="primary"):
                with st.spinner("Generating universal forecast..."):
                    forecast = universal_forecast(data, date_col, target_col, forecast_periods)

                if forecast is not None:
                    st.success("✅ Universal forecast generated successfully!")

                    # Store forecast in session state
                    st.session_state.forecast_data = forecast

                    # Display forecast metrics
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric(f"Avg Forecasted {target_col}", f"{forecast['Forecast'].mean():.1f}")
                    with col2:
                        st.metric(f"Total Forecasted {target_col}", f"{forecast['Forecast'].sum():.0f}")
                    with col3:
                        st.metric(f"Peak {target_col}", f"{forecast['Forecast'].max():.1f}")
            
                # Universal forecast visualization
                st.subheader("📈 Forecast Visualization")

                fig = go.Figure()

                # Historical data (last 60 days)
                historical_data = data[[date_col, target_col]].copy()
                historical_data[date_col] = pd.to_datetime(historical_data[date_col])
                historical_data = historical_data.sort_values(date_col)
                recent_data = historical_data.tail(min(60, len(historical_data)))

                fig.add_trace(go.Scatter(
                    x=recent_data[date_col],
                    y=recent_data[target_col],
                    mode='lines',
                    name=f'Historical {target_col}',
                    line=dict(color='blue')
                ))

                # Forecast
                fig.add_trace(go.Scatter(
                    x=forecast['Date'],
                    y=forecast['Forecast'],
                    mode='lines',
                    name='Forecast',
                    line=dict(color='red', dash='dash')
                ))

                # Confidence interval
                fig.add_trace(go.Scatter(
                    x=forecast['Date'],
                    y=forecast['Upper'],
                    mode='lines',
                    line=dict(width=0),
                    showlegend=False
                ))

                fig.add_trace(go.Scatter(
                    x=forecast['Date'],
                    y=forecast['Lower'],
                    mode='lines',
                    line=dict(width=0),
                    fill='tonexty',
                    fillcolor='rgba(255, 0, 0, 0.2)',
                    name='Confidence Interval'
                ))

                fig.update_layout(
                    title=f'{target_col} Forecast',
                    xaxis_title='Date',
                    yaxis_title=target_col,
                    height=500
                )

                st.plotly_chart(fig, use_container_width=True)

                # Forecast table
                st.subheader("📋 Forecast Details")
                display_forecast = forecast.copy()
                display_forecast['Date'] = display_forecast['Date'].dt.strftime('%Y-%m-%d')
                display_forecast = display_forecast.round(2)
                st.dataframe(display_forecast, use_container_width=True)

                # Forecast insights
                st.subheader("💡 Forecast Insights")

                trend = forecast['Forecast'].iloc[-1] - forecast['Forecast'].iloc[0]
                if trend > 0:
                    st.success(f"📈 Upward trend detected: {target_col} is expected to increase by {trend:.1f} over the forecast period.")
                elif trend < 0:
                    st.warning(f"📉 Downward trend detected: {target_col} is expected to decrease by {abs(trend):.1f} over the forecast period.")
                else:
                    st.info(f"➡️ Stable trend: {target_col} is expected to remain relatively stable.")
            else:
                st.error("❌ Failed to generate forecast. Please check your data and try again.")

elif page == "📈 Advanced Analytics":
    st.title("📈 Advanced Analytics")

    if not st.session_state.data_loaded:
        st.markdown("""
        <div class="warning-box">
        ⚠️ <strong>No data loaded.</strong> Please upload your data first.
        </div>
        """, unsafe_allow_html=True)
    else:
        data = st.session_state.uploaded_data
        column_types = detect_column_types(data)
        numerical_cols = [col for col, type_ in column_types.items() if type_ in ['numerical', 'numerical_target']]

        if not numerical_cols:
            st.error("❌ Advanced analytics requires numerical columns in your data.")
        else:
            st.subheader("🔬 Statistical Analysis")

            # Select columns for analysis
            selected_cols = st.multiselect(
                "Select columns for advanced analysis:",
                numerical_cols,
                default=numerical_cols[:3] if len(numerical_cols) >= 3 else numerical_cols
            )

            if selected_cols:
                # Advanced statistics
                col1, col2 = st.columns(2)

            with col1:
                st.markdown("**📊 Descriptive Statistics**")
                stats = data[selected_cols].describe()
                st.dataframe(stats, use_container_width=True)

            with col2:
                st.markdown("**🔗 Correlation Matrix**")
                if len(selected_cols) > 1:
                    corr = data[selected_cols].corr()
                    fig_corr = px.imshow(corr, text_auto=True, aspect="auto")
                    st.plotly_chart(fig_corr, use_container_width=True)
                else:
                    st.info("Select at least 2 columns for correlation analysis")

            # Distribution analysis
            st.subheader("📈 Distribution Analysis")

            for col in selected_cols[:2]:  # Show first 2 distributions
                col1, col2 = st.columns(2)

                with col1:
                    fig_hist = px.histogram(data, x=col, title=f'Distribution of {col}')
                    st.plotly_chart(fig_hist, use_container_width=True)

                with col2:
                    fig_box = px.box(data, y=col, title=f'Box Plot of {col}')
                    st.plotly_chart(fig_box, use_container_width=True)

            # Outlier detection
            st.subheader("🎯 Outlier Detection")

            outlier_col = st.selectbox("Select column for outlier analysis:", selected_cols)

            # Calculate outliers using IQR method
            Q1 = data[outlier_col].quantile(0.25)
            Q3 = data[outlier_col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR

            outliers = data[(data[outlier_col] < lower_bound) | (data[outlier_col] > upper_bound)]

            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Total Outliers", len(outliers))
            with col2:
                st.metric("Outlier Percentage", f"{len(outliers)/len(data)*100:.1f}%")
            with col3:
                st.metric("Normal Range", f"{lower_bound:.1f} - {upper_bound:.1f}")

            if len(outliers) > 0:
                st.subheader("🔍 Outlier Details")
                st.dataframe(outliers.head(10), use_container_width=True)

elif page == "📋 Reports":
    st.title("📋 Reports & Export")

    if not st.session_state.data_loaded:
        st.markdown("""
        <div class="warning-box">
        ⚠️ <strong>No data loaded.</strong> Please upload your data first.
        </div>
        """, unsafe_allow_html=True)
    else:
        data = st.session_state.uploaded_data

        st.subheader("📊 Data Summary Report")

        # Generate comprehensive report
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**📈 Dataset Overview**")
            st.write(f"• **Total Records:** {len(data):,}")
            st.write(f"• **Total Columns:** {len(data.columns)}")
            st.write(f"• **Memory Usage:** {data.memory_usage(deep=True).sum() / 1024**2:.1f} MB")

            # Column types
            column_types = detect_column_types(data)
            type_counts = {}
            for col_type in column_types.values():
                type_counts[col_type] = type_counts.get(col_type, 0) + 1

            st.markdown("**🏷️ Column Types:**")
            for col_type, count in type_counts.items():
                st.write(f"• **{col_type.replace('_', ' ').title()}:** {count}")

        with col2:
            st.markdown("**🔍 Data Quality**")

            # Missing values
            missing_total = data.isnull().sum().sum()
            st.write(f"• **Missing Values:** {missing_total:,}")

            # Duplicates
            duplicates = data.duplicated().sum()
            st.write(f"• **Duplicate Rows:** {duplicates:,}")

            # Data completeness
            completeness = (1 - missing_total / (len(data) * len(data.columns))) * 100
            st.write(f"• **Data Completeness:** {completeness:.1f}%")

        # Export options
        st.subheader("📤 Export Options")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 Download CSV", type="primary", use_container_width=True):
                csv = data.to_csv(index=False)
                st.download_button(
                    label="💾 Download CSV File",
                    data=csv,
                    file_name=f"data_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

        with col2:
            if st.button("📈 Download Summary", type="primary", use_container_width=True):
                summary = data.describe()
                summary_csv = summary.to_csv()
                st.download_button(
                    label="💾 Download Summary",
                    data=summary_csv,
                    file_name=f"data_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )

        with col3:
            if st.button("🔍 Download Report", type="primary", use_container_width=True):
                st.info("📋 Comprehensive PDF report functionality ready for implementation")

elif page == "📦 Inventory Planning":
    st.title("📦 Smart Planning & Optimization")

    if not st.session_state.data_loaded:
        st.markdown("""
        <div class="warning-box">
        ⚠️ <strong>No data loaded.</strong> This section provides planning tools based on your forecasts.
        </div>
        """, unsafe_allow_html=True)
    elif 'forecast_data' not in st.session_state:
        st.markdown("""
        <div class="warning-box">
        ⚠️ <strong>No forecast available.</strong> Please generate a forecast first to use planning tools.
        </div>
        """, unsafe_allow_html=True)
    else:
        forecast = st.session_state.forecast_data
        data = st.session_state.uploaded_data

        st.markdown("""
        <div class="insight-box">
        💡 <strong>Smart Planning Tools:</strong> Use your forecasts to make data-driven decisions for inventory,
        resource allocation, or capacity planning.
        </div>
        """, unsafe_allow_html=True)
        
        # Inventory configuration
        st.subheader("⚙️ Inventory Configuration")
        col1, col2 = st.columns(2)
        
        with col1:
            buffer_pct = st.slider("Safety Stock Buffer (%)", 5, 50, 20)
        with col2:
            current_stock = st.number_input("Current Stock Level", min_value=0, value=100)
        
        if st.button("📦 Generate Inventory Plan", type="primary"):
            with st.spinner("Calculating inventory recommendations..."):
                inventory_plan = calculate_inventory_plan(forecast, buffer_pct, current_stock)
            
            st.success("✅ Inventory plan generated successfully!")
            
            # Key metrics
            col1, col2, col3 = st.columns(3)
            with col1:
                total_orders = inventory_plan['Order_Quantity'].sum()
                st.metric("Total Orders Needed", f"{total_orders:.0f}")
            with col2:
                reorder_days = len(inventory_plan[inventory_plan['Order_Quantity'] > 0])
                st.metric("Reorder Days", reorder_days)
            with col3:
                avg_stock = inventory_plan['Current_Stock'].mean()
                st.metric("Avg Stock Level", f"{avg_stock:.1f}")
            
            # Inventory visualization
            st.subheader("📈 Inventory Dashboard")
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=inventory_plan['Date'],
                y=inventory_plan['Forecast'],
                mode='lines+markers',
                name='Predicted Demand',
                line=dict(color='blue')
            ))
            
            fig.add_trace(go.Scatter(
                x=inventory_plan['Date'],
                y=inventory_plan['Recommended_Stock'],
                mode='lines',
                name='Recommended Stock',
                line=dict(color='green', dash='dash')
            ))
            
            fig.add_trace(go.Scatter(
                x=inventory_plan['Date'],
                y=inventory_plan['Current_Stock'],
                mode='lines+markers',
                name='Current Stock',
                line=dict(color='orange')
            ))
            
            # Highlight reorder points
            reorder_points = inventory_plan[inventory_plan['Order_Quantity'] > 0]
            if not reorder_points.empty:
                fig.add_trace(go.Scatter(
                    x=reorder_points['Date'],
                    y=reorder_points['Current_Stock'],
                    mode='markers',
                    name='Reorder Points',
                    marker=dict(color='red', size=10, symbol='triangle-up')
                ))
            
            fig.update_layout(
                title='Inventory Management Dashboard',
                xaxis_title='Date',
                yaxis_title='Quantity',
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Inventory plan table
            st.subheader("📋 Inventory Plan Details")
            display_plan = inventory_plan.copy()
            display_plan['Date'] = display_plan['Date'].dt.strftime('%Y-%m-%d')
            display_plan = display_plan.round(2)
            
            # Color code the status
            def highlight_status(row):
                if row['Status'] == 'Reorder Needed':
                    return ['background-color: #ffebee'] * len(row)
                else:
                    return [''] * len(row)
            
            styled_plan = display_plan.style.apply(highlight_status, axis=1)
            st.dataframe(styled_plan, use_container_width=True)
            
            # Action items
            st.subheader("✅ Action Items")
            reorder_needed = len(inventory_plan[inventory_plan['Status'] == 'Reorder Needed'])
            if reorder_needed > 0:
                st.warning(f"📦 {reorder_needed} reorders needed in the forecast period")
            else:
                st.success("✅ No immediate reorders required")
            
            # Export simulation
            st.subheader("📤 Export Options")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("📊 Download Excel Report"):
                    st.info("Excel export functionality ready for implementation")
            with col2:
                if st.button("📄 Download PDF Report"):
                    st.info("PDF export functionality ready for implementation")

# Enhanced Footer
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666; padding: 2rem; background: linear-gradient(90deg, #f8f9fa, #e9ecef); border-radius: 1rem; margin-top: 2rem;'>
        <h4 style='color: #1f77b4; margin: 0;'>📊 Universal Data Analysis System</h4>
        <p style='margin: 0.5rem 0;'>Analyze Any Data • Generate Insights • Make Predictions</p>
        <p style='margin: 0; font-size: 0.9rem;'>Built with ❤️ using Streamlit, Plotly, and Pandas</p>
    </div>
    """,
    unsafe_allow_html=True
)
