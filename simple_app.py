"""
Simple Inventory Prediction System Demo
A streamlined version to demonstrate core functionality
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Page configuration
st.set_page_config(
    page_title="Inventory Prediction System",
    page_icon="📦",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False
if 'sample_data' not in st.session_state:
    st.session_state.sample_data = None

def create_sample_data():
    """Create sample sales data for demonstration"""
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    np.random.seed(42)
    
    # Create realistic sales pattern with trend and seasonality
    trend = np.linspace(100, 150, len(dates))
    seasonal = 20 * np.sin(2 * np.pi * np.arange(len(dates)) / 365.25)
    weekly = 10 * np.sin(2 * np.pi * np.arange(len(dates)) / 7)
    noise = np.random.normal(0, 15, len(dates))
    
    sales = trend + seasonal + weekly + noise
    sales = np.maximum(sales, 10)  # Ensure positive sales
    
    data = pd.DataFrame({
        'Date': dates,
        'Product': 'Product A',
        'Sales': sales.round(0).astype(int),
        'Store': 'Store 1',
        'Price': 10.99
    })
    
    return data

def simple_forecast(data, periods=30):
    """Simple moving average forecast"""
    recent_sales = data['Sales'].tail(30).mean()
    trend = (data['Sales'].tail(7).mean() - data['Sales'].head(7).mean()) / len(data)
    
    future_dates = pd.date_range(start=data['Date'].max() + timedelta(days=1), periods=periods)
    forecast_values = []
    
    for i in range(periods):
        forecast = recent_sales + (trend * i)
        # Add some uncertainty
        uncertainty = forecast * 0.1
        forecast_values.append({
            'Date': future_dates[i],
            'Forecast': max(forecast, 0),
            'Lower': max(forecast - uncertainty, 0),
            'Upper': forecast + uncertainty
        })
    
    return pd.DataFrame(forecast_values)

def calculate_inventory_plan(forecast_df, buffer_pct=20, current_stock=100):
    """Calculate inventory recommendations"""
    inventory_plan = forecast_df.copy()
    inventory_plan['Buffer_Stock'] = inventory_plan['Forecast'] * (buffer_pct / 100)
    inventory_plan['Recommended_Stock'] = inventory_plan['Forecast'] + inventory_plan['Buffer_Stock']
    
    # Simple reorder logic
    inventory_plan['Current_Stock'] = current_stock
    inventory_plan['Order_Quantity'] = 0
    inventory_plan['Status'] = 'Normal'
    
    for i in range(len(inventory_plan)):
        if i > 0:
            prev_stock = inventory_plan.iloc[i-1]['Current_Stock']
            demand = inventory_plan.iloc[i-1]['Forecast']
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Current_Stock')] = max(0, prev_stock - demand)
        
        current = inventory_plan.iloc[i]['Current_Stock']
        recommended = inventory_plan.iloc[i]['Recommended_Stock']
        
        if current < recommended * 0.5:
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Order_Quantity')] = recommended - current
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Status')] = 'Reorder Needed'
            inventory_plan.iloc[i, inventory_plan.columns.get_loc('Current_Stock')] = recommended
    
    return inventory_plan

# Sidebar
st.sidebar.title("📦 Inventory Prediction System")
st.sidebar.markdown("---")

page = st.sidebar.selectbox("Navigate to:", [
    "🏠 Home",
    "📊 Data & Analysis", 
    "🔮 Forecasting",
    "📦 Inventory Planning"
])

# Main content
if page == "🏠 Home":
    st.markdown('<h1 class="main-header">📦 Inventory Prediction System</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    Welcome to the **Inventory Prediction System** - your comprehensive solution for demand forecasting 
    and intelligent inventory management.
    
    ## 🚀 Key Features
    
    - **📊 Data Processing**: Analyze historical sales data
    - **📈 Exploratory Analysis**: Interactive visualizations and insights
    - **🔮 Forecasting**: Predict future demand patterns
    - **📦 Smart Inventory**: Automated reorder recommendations
    - **📋 Professional Reports**: Export-ready analysis
    
    ## 🎯 How to Get Started
    
    1. **Explore Data**: View sample sales data and patterns
    2. **Generate Forecast**: Predict future demand
    3. **Plan Inventory**: Get smart reorder recommendations
    4. **Analyze Results**: Review insights and recommendations
    
    Click on the navigation menu to explore different sections!
    """)
    
    # Load sample data
    if st.button("🔄 Load Sample Data", type="primary"):
        st.session_state.sample_data = create_sample_data()
        st.session_state.data_loaded = True
        st.success("✅ Sample data loaded successfully!")
        st.rerun()

elif page == "📊 Data & Analysis":
    st.title("📊 Data & Analysis")
    
    if not st.session_state.data_loaded:
        st.warning("⚠️ Please load sample data from the Home page first.")
        if st.button("🔄 Load Sample Data"):
            st.session_state.sample_data = create_sample_data()
            st.session_state.data_loaded = True
            st.rerun()
    else:
        data = st.session_state.sample_data
        
        # Data overview
        st.subheader("📋 Data Overview")
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Records", len(data))
        with col2:
            st.metric("Date Range", f"{(data['Date'].max() - data['Date'].min()).days} days")
        with col3:
            st.metric("Total Sales", f"{data['Sales'].sum():,}")
        with col4:
            st.metric("Avg Daily Sales", f"{data['Sales'].mean():.1f}")
        
        # Data preview
        st.subheader("👀 Data Preview")
        st.dataframe(data.head(10), use_container_width=True)
        
        # Sales trend
        st.subheader("📈 Sales Trend")
        fig = px.line(data, x='Date', y='Sales', title='Daily Sales Over Time')
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)
        
        # Monthly analysis
        st.subheader("📅 Monthly Analysis")
        monthly_data = data.copy()
        monthly_data['Month'] = monthly_data['Date'].dt.month_name()
        monthly_sales = monthly_data.groupby('Month')['Sales'].mean().reindex([
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ])
        
        fig_monthly = px.bar(x=monthly_sales.index, y=monthly_sales.values, 
                           title='Average Sales by Month')
        st.plotly_chart(fig_monthly, use_container_width=True)

elif page == "🔮 Forecasting":
    st.title("🔮 Demand Forecasting")
    
    if not st.session_state.data_loaded:
        st.warning("⚠️ Please load sample data from the Home page first.")
    else:
        data = st.session_state.sample_data
        
        # Forecast configuration
        st.subheader("⚙️ Forecast Configuration")
        col1, col2 = st.columns(2)
        
        with col1:
            forecast_days = st.selectbox("Forecast Period", [30, 60, 90], index=0)
        with col2:
            st.info("Using Simple Moving Average Model")
        
        if st.button("🔮 Generate Forecast", type="primary"):
            with st.spinner("Generating forecast..."):
                forecast = simple_forecast(data, forecast_days)
            
            st.success("✅ Forecast generated successfully!")
            
            # Store forecast in session state
            st.session_state.forecast_data = forecast
            
            # Display forecast metrics
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Avg Forecasted Demand", f"{forecast['Forecast'].mean():.1f}")
            with col2:
                st.metric("Total Forecasted Demand", f"{forecast['Forecast'].sum():.0f}")
            with col3:
                st.metric("Peak Demand", f"{forecast['Forecast'].max():.1f}")
            
            # Forecast visualization
            st.subheader("📈 Forecast Visualization")
            
            fig = go.Figure()
            
            # Historical data (last 60 days)
            recent_data = data.tail(60)
            fig.add_trace(go.Scatter(
                x=recent_data['Date'],
                y=recent_data['Sales'],
                mode='lines',
                name='Historical Sales',
                line=dict(color='blue')
            ))
            
            # Forecast
            fig.add_trace(go.Scatter(
                x=forecast['Date'],
                y=forecast['Forecast'],
                mode='lines',
                name='Forecast',
                line=dict(color='red', dash='dash')
            ))
            
            # Confidence interval
            fig.add_trace(go.Scatter(
                x=forecast['Date'],
                y=forecast['Upper'],
                mode='lines',
                line=dict(width=0),
                showlegend=False
            ))
            
            fig.add_trace(go.Scatter(
                x=forecast['Date'],
                y=forecast['Lower'],
                mode='lines',
                line=dict(width=0),
                fill='tonexty',
                fillcolor='rgba(255, 0, 0, 0.2)',
                name='Confidence Interval'
            ))
            
            fig.update_layout(
                title='Sales Forecast',
                xaxis_title='Date',
                yaxis_title='Sales',
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Forecast table
            st.subheader("📋 Forecast Details")
            display_forecast = forecast.copy()
            display_forecast['Date'] = display_forecast['Date'].dt.strftime('%Y-%m-%d')
            display_forecast = display_forecast.round(2)
            st.dataframe(display_forecast, use_container_width=True)

elif page == "📦 Inventory Planning":
    st.title("📦 Inventory Planning")
    
    if 'forecast_data' not in st.session_state:
        st.warning("⚠️ Please generate a forecast first.")
    else:
        forecast = st.session_state.forecast_data
        
        # Inventory configuration
        st.subheader("⚙️ Inventory Configuration")
        col1, col2 = st.columns(2)
        
        with col1:
            buffer_pct = st.slider("Safety Stock Buffer (%)", 5, 50, 20)
        with col2:
            current_stock = st.number_input("Current Stock Level", min_value=0, value=100)
        
        if st.button("📦 Generate Inventory Plan", type="primary"):
            with st.spinner("Calculating inventory recommendations..."):
                inventory_plan = calculate_inventory_plan(forecast, buffer_pct, current_stock)
            
            st.success("✅ Inventory plan generated successfully!")
            
            # Key metrics
            col1, col2, col3 = st.columns(3)
            with col1:
                total_orders = inventory_plan['Order_Quantity'].sum()
                st.metric("Total Orders Needed", f"{total_orders:.0f}")
            with col2:
                reorder_days = len(inventory_plan[inventory_plan['Order_Quantity'] > 0])
                st.metric("Reorder Days", reorder_days)
            with col3:
                avg_stock = inventory_plan['Current_Stock'].mean()
                st.metric("Avg Stock Level", f"{avg_stock:.1f}")
            
            # Inventory visualization
            st.subheader("📈 Inventory Dashboard")
            
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=inventory_plan['Date'],
                y=inventory_plan['Forecast'],
                mode='lines+markers',
                name='Predicted Demand',
                line=dict(color='blue')
            ))
            
            fig.add_trace(go.Scatter(
                x=inventory_plan['Date'],
                y=inventory_plan['Recommended_Stock'],
                mode='lines',
                name='Recommended Stock',
                line=dict(color='green', dash='dash')
            ))
            
            fig.add_trace(go.Scatter(
                x=inventory_plan['Date'],
                y=inventory_plan['Current_Stock'],
                mode='lines+markers',
                name='Current Stock',
                line=dict(color='orange')
            ))
            
            # Highlight reorder points
            reorder_points = inventory_plan[inventory_plan['Order_Quantity'] > 0]
            if not reorder_points.empty:
                fig.add_trace(go.Scatter(
                    x=reorder_points['Date'],
                    y=reorder_points['Current_Stock'],
                    mode='markers',
                    name='Reorder Points',
                    marker=dict(color='red', size=10, symbol='triangle-up')
                ))
            
            fig.update_layout(
                title='Inventory Management Dashboard',
                xaxis_title='Date',
                yaxis_title='Quantity',
                height=500
            )
            
            st.plotly_chart(fig, use_container_width=True)
            
            # Inventory plan table
            st.subheader("📋 Inventory Plan Details")
            display_plan = inventory_plan.copy()
            display_plan['Date'] = display_plan['Date'].dt.strftime('%Y-%m-%d')
            display_plan = display_plan.round(2)
            
            # Color code the status
            def highlight_status(row):
                if row['Status'] == 'Reorder Needed':
                    return ['background-color: #ffebee'] * len(row)
                else:
                    return [''] * len(row)
            
            styled_plan = display_plan.style.apply(highlight_status, axis=1)
            st.dataframe(styled_plan, use_container_width=True)
            
            # Action items
            st.subheader("✅ Action Items")
            reorder_needed = len(inventory_plan[inventory_plan['Status'] == 'Reorder Needed'])
            if reorder_needed > 0:
                st.warning(f"📦 {reorder_needed} reorders needed in the forecast period")
            else:
                st.success("✅ No immediate reorders required")
            
            # Export simulation
            st.subheader("📤 Export Options")
            col1, col2 = st.columns(2)
            with col1:
                if st.button("📊 Download Excel Report"):
                    st.info("Excel export functionality ready for implementation")
            with col2:
                if st.button("📄 Download PDF Report"):
                    st.info("PDF export functionality ready for implementation")

# Footer
st.markdown("---")
st.markdown(
    """
    <div style='text-align: center; color: #666; padding: 1rem;'>
        📦 Inventory Prediction System Demo | Built with Streamlit
    </div>
    """, 
    unsafe_allow_html=True
)
