"""
Universal Data Analysis System - Main Streamlit Application
A comprehensive web-based system for analyzing any type of data with forecasting capabilities.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Try to import advanced modules, fall back to basic functionality
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False

try:
    from statsmodels.tsa.arima.model import ARIMA
    ARIMA_AVAILABLE = True
except ImportError:
    ARIMA_AVAILABLE = False

try:
    import openpyxl
    EXCEL_EXPORT_AVAILABLE = True
except ImportError:
    EXCEL_EXPORT_AVAILABLE = False

# Page configuration
st.set_page_config(
    page_title="Universal Data Analysis System",
    page_icon="�",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .alert-critical {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
        padding: 1rem;
        margin: 1rem 0;
    }
    .alert-warning {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 1rem;
        margin: 1rem 0;
    }
    .alert-info {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        padding: 1rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
def initialize_session_state():
    """Initialize session state variables."""
    if 'data_loaded' not in st.session_state:
        st.session_state.data_loaded = False
    if 'cleaned_data' not in st.session_state:
        st.session_state.cleaned_data = None
    if 'forecast_data' not in st.session_state:
        st.session_state.forecast_data = None
    if 'inventory_data' not in st.session_state:
        st.session_state.inventory_data = None
    if 'column_mapping' not in st.session_state:
        st.session_state.column_mapping = {}
    if 'show_code' not in st.session_state:
        st.session_state.show_code = False

# Sidebar navigation
def create_sidebar():
    """Create sidebar navigation."""
    st.sidebar.title("📦 Inventory Prediction System")
    st.sidebar.markdown("---")
    
    # Navigation menu
    pages = {
        "🏠 Home": "home",
        "📊 Data Upload & Cleaning": "data_upload",
        "📈 Exploratory Data Analysis": "eda", 
        "🔮 Forecasting": "forecasting",
        "📦 Inventory Planning": "inventory",
        "📋 Reports & Export": "reports",
        "🤖 AI Assistant": "ai_assistant"
    }
    
    selected_page = st.sidebar.selectbox("Navigate to:", list(pages.keys()))
    
    st.sidebar.markdown("---")
    
    # Settings
    st.sidebar.subheader("⚙️ Settings")
    st.session_state.show_code = st.sidebar.toggle("Show Code View", value=st.session_state.show_code)
    
    # Data status
    st.sidebar.subheader("📊 Data Status")
    if st.session_state.data_loaded:
        st.sidebar.success("✅ Data Loaded")
        if st.session_state.cleaned_data is not None:
            st.sidebar.info(f"📋 {len(st.session_state.cleaned_data)} rows")
    else:
        st.sidebar.warning("⚠️ No Data Loaded")
    
    if st.session_state.forecast_data is not None:
        st.sidebar.success("✅ Forecast Generated")
    
    if st.session_state.inventory_data is not None:
        st.sidebar.success("✅ Inventory Plan Ready")
    
    return pages[selected_page]

# Home page
def show_home_page():
    """Display the home page."""
    st.markdown('<h1 class="main-header">📦 Inventory Prediction System</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    Welcome to the **Inventory Prediction System** - your comprehensive solution for demand forecasting 
    and intelligent inventory management.
    
    ## 🚀 Key Features
    
    - **📊 Data Processing**: Upload and clean historical sales data (CSV/Excel)
    - **📈 Exploratory Analysis**: Interactive visualizations and insights
    - **🔮 ML Forecasting**: Prophet and ARIMA models for demand prediction
    - **📦 Smart Inventory**: Automated reorder recommendations with buffer stock
    - **📋 Professional Reports**: Export to Excel and PDF formats
    - **🤖 AI Assistant**: Get help and explanations for your analysis
    
    ## 🎯 How to Get Started
    
    1. **Upload Data**: Go to "Data Upload & Cleaning" to upload your sales data
    2. **Explore**: Use "Exploratory Data Analysis" to understand your data patterns
    3. **Forecast**: Generate predictions using "Forecasting" with ML models
    4. **Plan Inventory**: Get smart recommendations in "Inventory Planning"
    5. **Export Results**: Download professional reports from "Reports & Export"
    
    ## 📋 Data Requirements
    
    Your data should include these columns:
    - **Date**: Sales date (any common date format)
    - **Sales/Demand**: Quantity sold or demanded
    - **Product** (optional): Product identifier
    - **Store** (optional): Store or location identifier
    - **Price** (optional): Unit price information
    
    ## 🔧 Supported Formats
    - CSV files (.csv)
    - Excel files (.xlsx, .xls)
    """)
    
    # Quick stats if data is loaded
    if st.session_state.data_loaded and st.session_state.cleaned_data is not None:
        st.markdown("## 📊 Current Data Overview")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Records", len(st.session_state.cleaned_data))
        
        with col2:
            date_cols = st.session_state.cleaned_data.select_dtypes(include=['datetime64[ns]']).columns
            if len(date_cols) > 0:
                date_range = (st.session_state.cleaned_data[date_cols[0]].max() - 
                            st.session_state.cleaned_data[date_cols[0]].min()).days
                st.metric("Date Range (Days)", date_range)
        
        with col3:
            st.metric("Columns", len(st.session_state.cleaned_data.columns))
        
        with col4:
            if st.session_state.forecast_data is not None:
                st.metric("Forecast Days", len(st.session_state.forecast_data))

# Main application
def main():
    """Main application function."""
    if not MODULES_AVAILABLE:
        st.error("❌ Required modules are not available.")
        st.info("🔄 Please use simple_app.py for a working demo or install missing dependencies.")
        st.code("streamlit run simple_app.py", language="bash")
        return

    initialize_session_state()

    # Create sidebar and get selected page
    current_page = create_sidebar()
    
    # Route to appropriate page
    if current_page == "home":
        show_home_page()
    elif current_page == "data_upload":
        from pages.data_upload import show_data_upload_page
        show_data_upload_page()
    elif current_page == "eda":
        from pages.eda import show_eda_page
        show_eda_page()
    elif current_page == "forecasting":
        from pages.forecasting import show_forecasting_page
        show_forecasting_page()
    elif current_page == "inventory":
        from pages.inventory import show_inventory_page
        show_inventory_page()
    elif current_page == "reports":
        from pages.reports import show_reports_page
        show_reports_page()
    elif current_page == "ai_assistant":
        from pages.ai_assistant import show_ai_assistant_page
        show_ai_assistant_page()
    
    # Footer
    st.markdown("---")
    st.markdown(
        """
        <div style='text-align: center; color: #666; padding: 1rem;'>
            📦 Inventory Prediction System | Built with Streamlit | 
            <a href='https://github.com' target='_blank'>View on GitHub</a>
        </div>
        """, 
        unsafe_allow_html=True
    )

if __name__ == "__main__":
    main()
