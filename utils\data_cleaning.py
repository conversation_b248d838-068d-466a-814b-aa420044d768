"""
Data cleaning utilities for the Inventory Prediction System.
This module handles data preprocessing, cleaning, and validation.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import streamlit as st
import warnings
warnings.filterwarnings('ignore')


def load_data(uploaded_file):
    """
    Load data from uploaded CSV or Excel file.
    
    Args:
        uploaded_file: Streamlit uploaded file object
        
    Returns:
        pandas.DataFrame: Loaded dataframe
    """
    try:
        if uploaded_file.name.endswith('.csv'):
            df = pd.read_csv(uploaded_file)
        elif uploaded_file.name.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(uploaded_file)
        else:
            st.error("Unsupported file format. Please upload CSV or Excel files.")
            return None
        
        st.success(f"Data loaded successfully! Shape: {df.shape}")
        return df
    
    except Exception as e:
        st.error(f"Error loading file: {str(e)}")
        return None


def detect_date_column(df):
    """
    Automatically detect the date column in the dataframe.
    
    Args:
        df (pandas.DataFrame): Input dataframe
        
    Returns:
        str: Name of the detected date column
    """
    date_keywords = ['date', 'time', 'day', 'month', 'year', 'created', 'updated']
    
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in date_keywords):
            return col
    
    # If no keyword match, check data types
    for col in df.columns:
        if df[col].dtype == 'datetime64[ns]':
            return col
        
        # Try to parse as date
        try:
            pd.to_datetime(df[col].head(10))
            return col
        except:
            continue
    
    return None


def clean_data(df, date_col=None, product_col=None, sales_col=None, store_col=None):
    """
    Clean and preprocess the data for analysis.
    
    Args:
        df (pandas.DataFrame): Input dataframe
        date_col (str): Name of date column
        product_col (str): Name of product column
        sales_col (str): Name of sales column
        store_col (str): Name of store column
        
    Returns:
        pandas.DataFrame: Cleaned dataframe
        dict: Cleaning summary statistics
    """
    df_clean = df.copy()
    cleaning_summary = {}
    
    # Record original shape
    original_shape = df_clean.shape
    cleaning_summary['original_rows'] = original_shape[0]
    cleaning_summary['original_columns'] = original_shape[1]
    
    # Handle date column
    if date_col:
        try:
            df_clean[date_col] = pd.to_datetime(df_clean[date_col])
            cleaning_summary['date_parsing'] = 'Success'
        except Exception as e:
            st.warning(f"Could not parse date column: {str(e)}")
            cleaning_summary['date_parsing'] = f'Failed: {str(e)}'
    
    # Handle missing values
    missing_before = df_clean.isnull().sum().sum()
    
    # Fill missing numerical values with median
    numerical_cols = df_clean.select_dtypes(include=[np.number]).columns
    for col in numerical_cols:
        if df_clean[col].isnull().sum() > 0:
            median_val = df_clean[col].median()
            df_clean[col].fillna(median_val, inplace=True)
    
    # Fill missing categorical values with mode
    categorical_cols = df_clean.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        if col != date_col and df_clean[col].isnull().sum() > 0:
            mode_val = df_clean[col].mode().iloc[0] if len(df_clean[col].mode()) > 0 else 'Unknown'
            df_clean[col].fillna(mode_val, inplace=True)
    
    missing_after = df_clean.isnull().sum().sum()
    cleaning_summary['missing_values_filled'] = missing_before - missing_after
    
    # Remove duplicates
    duplicates_before = df_clean.duplicated().sum()
    df_clean.drop_duplicates(inplace=True)
    duplicates_after = df_clean.duplicated().sum()
    cleaning_summary['duplicates_removed'] = duplicates_before - duplicates_after
    
    # Sort by date if date column exists
    if date_col and date_col in df_clean.columns:
        df_clean.sort_values(by=date_col, inplace=True)
        df_clean.reset_index(drop=True, inplace=True)
        cleaning_summary['sorted_by_date'] = True
    
    # Ensure sales column is numeric
    if sales_col and sales_col in df_clean.columns:
        try:
            df_clean[sales_col] = pd.to_numeric(df_clean[sales_col], errors='coerce')
            # Remove rows with negative sales
            negative_sales = (df_clean[sales_col] < 0).sum()
            df_clean = df_clean[df_clean[sales_col] >= 0]
            cleaning_summary['negative_sales_removed'] = negative_sales
        except Exception as e:
            st.warning(f"Could not convert sales column to numeric: {str(e)}")
    
    # Final shape
    final_shape = df_clean.shape
    cleaning_summary['final_rows'] = final_shape[0]
    cleaning_summary['final_columns'] = final_shape[1]
    cleaning_summary['rows_removed'] = original_shape[0] - final_shape[0]
    
    return df_clean, cleaning_summary


def validate_data_structure(df, required_columns=None):
    """
    Validate that the dataframe has the required structure for forecasting.
    
    Args:
        df (pandas.DataFrame): Input dataframe
        required_columns (list): List of required column names
        
    Returns:
        bool: True if valid, False otherwise
        list: List of validation messages
    """
    messages = []
    is_valid = True
    
    if df is None or df.empty:
        messages.append("❌ Dataframe is empty")
        return False, messages
    
    if required_columns:
        missing_cols = [col for col in required_columns if col not in df.columns]
        if missing_cols:
            messages.append(f"❌ Missing required columns: {missing_cols}")
            is_valid = False
        else:
            messages.append("✅ All required columns present")
    
    # Check for minimum number of rows
    if len(df) < 30:
        messages.append("⚠️ Dataset has less than 30 rows. Forecasting may be less accurate.")
    else:
        messages.append(f"✅ Dataset has {len(df)} rows")
    
    # Check date range
    date_cols = df.select_dtypes(include=['datetime64[ns]']).columns
    if len(date_cols) > 0:
        date_col = date_cols[0]
        date_range = df[date_col].max() - df[date_col].min()
        if date_range.days < 90:
            messages.append("⚠️ Date range is less than 90 days. Consider more historical data.")
        else:
            messages.append(f"✅ Date range: {date_range.days} days")
    
    return is_valid, messages


def get_column_info(df):
    """
    Get detailed information about dataframe columns.
    
    Args:
        df (pandas.DataFrame): Input dataframe
        
    Returns:
        dict: Column information
    """
    info = {}
    
    for col in df.columns:
        col_info = {
            'dtype': str(df[col].dtype),
            'null_count': df[col].isnull().sum(),
            'unique_count': df[col].nunique(),
            'sample_values': df[col].dropna().head(3).tolist()
        }
        
        if df[col].dtype in ['int64', 'float64']:
            col_info['min'] = df[col].min()
            col_info['max'] = df[col].max()
            col_info['mean'] = df[col].mean()
        
        info[col] = col_info
    
    return info


def prepare_for_forecasting(df, date_col, value_col, freq='D'):
    """
    Prepare data specifically for time series forecasting.
    
    Args:
        df (pandas.DataFrame): Input dataframe
        date_col (str): Name of date column
        value_col (str): Name of value column to forecast
        freq (str): Frequency for resampling ('D', 'W', 'M')
        
    Returns:
        pandas.DataFrame: Prepared dataframe with date and value columns
    """
    # Create a copy and ensure proper data types
    forecast_df = df[[date_col, value_col]].copy()
    forecast_df[date_col] = pd.to_datetime(forecast_df[date_col])
    forecast_df[value_col] = pd.to_numeric(forecast_df[value_col], errors='coerce')
    
    # Remove any rows with NaN values
    forecast_df.dropna(inplace=True)
    
    # Set date as index and resample
    forecast_df.set_index(date_col, inplace=True)
    forecast_df = forecast_df.resample(freq)[value_col].sum().reset_index()
    
    # Rename columns for consistency
    forecast_df.columns = ['ds', 'y']
    
    return forecast_df
